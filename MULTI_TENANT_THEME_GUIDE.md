# Multi-Tenant Theme System Guide

## Overview
This guide explains how to use the Multi-Tenant Theme System that dynamically applies colors from the backend API to your React components.

## Architecture

### 1. Configuration (`src/config/config.js`)
```javascript
export const MOODLE_CONFIG = {
  BASE_URL: '/webservice/rest/server.php',
  DEFAULT_WS_TOKEN: 'your_actual_token_here',
  DEFAULT_TENANT: {
    id: 83,
    name: 'Default Tenant',
    language: 'en'
  },
  FUNCTIONS: {
    GET_TENANT_HEADER_INFO: 'local_guestapi_get_tenant_header_info'
  }
};
```

### 2. Multi-Tenant API Service (`src/core/api/multiTenantApi.js`)
Handles all Moodle Web Service calls:
- `getTenantHeaderInfo()` - Fetches header information including colors
- `getTenantFooterInfo()` - Fetches footer information  
- `getTenantPrograms()` - Fetches programs list
- `testConnection()` - Tests API connectivity

### 3. Multi-Tenant Hook (`src/hooks/useMultiTenant.js`)
React hook that provides:
```javascript
const {
  headerInfo,           // Raw header data from API
  tenantName,          // Tenant name
  tenantLogo,          // Tenant logo URL
  tenantColors,        // Processed colors object
  availableServices,   // Available services array
  isConnected,         // Connection status
  loading,             // Loading state
  error,               // Error state
  refresh              // Function to refresh data
} = useMultiTenant(tenantId, language);
```

### 4. Theme Provider (`src/themes/TenantThemeProvider.jsx`)
Applies Multi-Tenant colors to Material-UI theme:
```javascript
const theme = {
  palette: {
    primary: { main: tenantColors.primary },
    secondary: { main: tenantColors.secondary },
    tertiary: { main: tenantColors.tertiary }
  }
};
```

## Usage Examples

### 1. Using Theme Colors in Components
```javascript
import { useTheme } from '@mui/material';

const MyComponent = () => {
  const theme = useTheme();
  
  const colors = {
    primary: theme.palette.primary.main,
    secondary: theme.palette.secondary.main,
    tertiary: theme.palette.tertiary?.main || theme.palette.info.main
  };
  
  return (
    <Box sx={{ backgroundColor: colors.primary }}>
      Content with tenant colors
    </Box>
  );
};
```

### 2. Using Multi-Tenant Hook Directly
```javascript
import { useMultiTenant } from '../hooks/useMultiTenant';

const MyComponent = () => {
  const { tenantColors, tenantName, isConnected } = useMultiTenant();
  
  return (
    <Box sx={{ color: tenantColors.primary }}>
      Welcome to {tenantName}
      {isConnected ? '✅' : '❌'}
    </Box>
  );
};
```

### 3. Dynamic Menu Based on Services
```javascript
const menuItems = [
  { label: 'Home', href: '/home' },
  ...(hasAboutUs ? [{ label: 'About us', href: '/about-us' }] : []),
  ...(availableServices?.length > 0 ? [{ label: 'Services', hasDropdown: true }] : []),
  ...(hasContactUs ? [{ label: 'Contact us', href: '/contact-us' }] : []),
  ...(hasFaq ? [{ label: 'FAQs', href: '/faq' }] : [])
];
```

### 4. Swiper with Tenant Colors
```javascript
<Swiper
  style={{
    '--swiper-pagination-color': tenantColors.primary,
    '--swiper-pagination-bullet-inactive-color': tenantColors.secondary,
  }}
  pagination={{ clickable: true }}
>
  {/* Slides */}
</Swiper>
```

## API Response Format

The backend should return data in this format:
```json
{
  "name": "dev",
  "expirationdate": "1781721000",
  "primarycolor": "#032E9B",
  "secondarycolor": "#FFE284", 
  "teritorycolor": "#FF8A19",
  "services": [
    {
      "id": "local_classroom",
      "name": "Classrooms"
    }
  ],
  "logo": "http://naahel.local/pluginfile.php/...",
  "aboutus": [{"link": "/aboutus", "status": "1"}],
  "contactus": "/contactus",
  "faq": [{"link": "/faq", "status": "1"}]
}
```

## Development Tools

### Console Commands
```javascript
// Test Multi-Tenant API
window.testMultiTenant.refreshData()

// View current data
window.testMultiTenant.currentData

// Test Moodle Header API directly
window.testMoodleHeaderAPI()
```

### Console Logs
The system provides detailed logging:
- 🎨 Multi-Tenant Colors (Raw)
- 🎭 Theme Colors (Applied)  
- 🏢 Multi-Tenant Info
- 🎭 TenantThemeProvider Status

### Visual Indicators
In development mode, you'll see status indicators:
- 🔄 Loading... (blue) - Loading data
- ✅ Tenant Name (green) - Connected successfully
- ❌ Failed (red) - Connection failed
- ⚠️ No Data (orange) - No data available

## Best Practices

1. **Always use theme colors**: Access colors through `useTheme()` hook
2. **Fallback colors**: Always provide fallback colors for offline scenarios
3. **Loading states**: Handle loading and error states appropriately
4. **Performance**: Use `React.memo()` for components that don't need frequent re-renders
5. **Testing**: Use the provided console commands for testing

## Troubleshooting

### Common Issues
1. **Colors not updating**: Check if TenantThemeProvider wraps your app
2. **API not connecting**: Verify token and tenant ID in config
3. **Theme not applied**: Ensure useTheme() is called inside ThemeProvider
4. **Console errors**: Check network tab for API call failures

### Debug Steps
1. Check console for Multi-Tenant logs
2. Verify API response format
3. Test with `window.testMultiTenant.refreshData()`
4. Check theme provider hierarchy
5. Validate token and tenant ID

## File Structure
```
src/
├── config/
│   └── config.js                 # Multi-Tenant configuration
├── core/api/
│   └── multiTenantApi.js        # API service
├── hooks/
│   └── useMultiTenant.js        # React hook
├── themes/
│   └── TenantThemeProvider.jsx  # Theme provider
├── styles/
│   └── multiTenantSwiper.css    # Swiper styles
└── layout/Simple/
    └── Header.jsx               # Example usage
```

This system provides a complete Multi-Tenant theming solution that automatically applies backend colors to your entire application.
