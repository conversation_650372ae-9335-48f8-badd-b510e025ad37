{"name": "able-pro-material-react-js", "version": "9.3.0", "private": true, "dependencies": {"@emotion/cache": "11.14.0", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@fontsource/inter": "5.1.1", "@fontsource/poppins": "5.1.1", "@fontsource/public-sans": "5.1.2", "@fontsource/roboto": "5.1.1", "@mui/base": "5.0.0-beta.68", "@mui/lab": "6.0.0-beta.23", "@mui/material": "6.4.0", "@mui/system": "6.4.0", "@vitejs/plugin-react": "4.3.4", "axios": "1.7.9", "chance": "1.1.12", "crypto-js": "^4.2.0", "formik": "2.4.6", "framer-motion": "11.15.0", "history": "5.3.0", "iconsax-react": "0.0.8", "jwt-decode": "4.0.0", "lodash-es": "4.17.21", "react": "18.3.1", "react-device-detect": "2.2.3", "react-dom": "18.3.1", "react-intl": "6.8.9", "react-otp-input": "3.1.1", "react-router": "7.1.1", "react-router-dom": "7.1.1", "react-timer-hook": "3.0.8", "react-toastify": "^11.0.5", "simplebar": "6.3.0", "simplebar-react": "3.3.0", "slick-carousel": "1.8.1", "stylis-plugin-rtl": "2.1.1", "swiper": "^11.2.8", "swr": "2.3.0", "vite": "5.4.11", "vite-jsconfig-paths": "2.0.1", "web-vitals": "4.2.4", "yup": "1.6.1"}, "scripts": {"start": "vite", "build": "vite build", "build-stage": "env-cmd -f .env.qa vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\"", "lint:fix": "eslint --fix \"src/**/*.{js,jsx,ts,tsx}\"", "prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx}\"", "knip": "knip"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/compat": "1.2.4", "@eslint/eslintrc": "3.2.0", "@eslint/js": "9.17.0", "env-cmd": "10.1.0", "eslint": "9.19.0", "eslint-config-prettier": "10.0.1", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-prettier": "5.2.3", "eslint-plugin-react": "7.37.4", "eslint-plugin-react-hooks": "5.1.0", "knip": "5.44.0", "prettier": "3.4.2"}, "packageManager": "yarn@4.7.0"}