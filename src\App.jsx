import React, { useEffect, useState } from 'react';
import { RouterProvider } from 'react-router-dom';

// project-imports
import router from 'routes';
import ThemeCustomization from 'themes';

import Locales from 'components/Locales';
import RTLLayout from 'components/RTLLayout';
import ScrollTop from 'components/ScrollTop';
import Snackbar from 'components/@extended/Snackbar';

// auth-provider
import { JWTProvider as AuthProvider } from 'contexts/JWTContext';
import { TenantProvider } from './contexts/TenantContext';

// tenant configuration
import { getTenantBySubdomain, extractSubdomain } from './config/tenants';

// ==============================|| APP - THEME, ROUTER, LOCAL  ||============================== //

export default function App() {
  const [tenantConfig, setTenantConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Extract subdomain and get tenant configuration
    const hostname = window.location.hostname;
    const subdomain = extractSubdomain(hostname);
    const config = getTenantBySubdomain(subdomain);

    // Set document title and direction based on tenant
    document.title = config.name;
    document.documentElement.dir = config.direction;
    document.documentElement.lang = config.language;

    setTenantConfig(config);
    setIsLoading(false);

    console.log('🏢 Tenant detected:', {
      subdomain,
      tenantName: config.name,
      tenantId: config.id,
      language: config.language,
      apiEndpoint: config.apiEndpoint
    });
  }, []);

  // Show loading while detecting tenant
  if (isLoading || !tenantConfig) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#f5f5f5'
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div>🔍 Detecting tenant...</div>
          <div style={{ fontSize: '12px', marginTop: '10px', opacity: 0.7 }}>Hostname: {window.location.hostname}</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <ThemeCustomization>
        <RTLLayout>
          <Locales>
            <ScrollTop>
              <AuthProvider>
                <TenantProvider initialTenant={tenantConfig}>
                  <>
                    <RouterProvider router={router} />
                    <Snackbar />
                  </>
                </TenantProvider>
              </AuthProvider>
            </ScrollTop>
          </Locales>
        </RTLLayout>
      </ThemeCustomization>
    </>
  );
}
