import React, { useEffect, useState } from 'react';
import { RouterProvider } from 'react-router-dom';

// project-imports
import router from 'routes';
import ThemeCustomization from 'themes';

import Locales from 'components/Locales';
import RTLLayout from 'components/RTLLayout';
import ScrollTop from 'components/ScrollTop';
import Snackbar from 'components/@extended/Snackbar';

// auth-provider
import { JWTProvider as AuthProvider } from 'contexts/JWTContext';
import { TenantProvider } from './contexts/TenantContext';

// tenant API
import { getTenantInfo } from './services/multiTenantApi';

// ==============================|| APP - THEME, ROUTER, LOCAL  ||============================== //

export default function App() {
  const [tenantConfig, setTenantConfig] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadTenantFromAPI = async () => {
      try {
        setIsLoading(true);

        // Get tenant info from API using current hostname
        const hostname = window.location.hostname;
        const tenantData = await getTenantInfo(hostname, null, 'ar');

        // Create tenant config from API response
        const config = {
          id: tenantData.id,
          name: tenantData.name,
          language: 'ar', // Default to Arabic, can be dynamic later
          direction: 'rtl', // Default to RTL for Arabic
          primaryColor: tenantData.primarycolor,
          secondaryColor: tenantData.secondarycolor,
          teritoryColor: tenantData.teritorycolor,
          logo: tenantData.logo,
          services: tenantData.services || []
        };

        // Set document properties based on API data
        document.title = config.name;
        document.documentElement.dir = config.direction;
        document.documentElement.lang = config.language;

        setTenantConfig(config);
      } catch (error) {
        console.error('❌ Failed to load tenant from API:', error);

        // Fallback configuration
        const fallbackConfig = {
          id: 83,
          name: 'Default Institution',
          language: 'en',
          direction: 'ltr',
          primaryColor: '#8c76dd',
          secondaryColor: '#FFE284',
          teritoryColor: '#FF8A19',
          logo: null,
          services: []
        };

        document.title = fallbackConfig.name;
        document.documentElement.dir = fallbackConfig.direction;
        document.documentElement.lang = fallbackConfig.language;

        setTenantConfig(fallbackConfig);

        console.log('🔄 Using fallback tenant config');
      } finally {
        setIsLoading(false);
      }
    };

    loadTenantFromAPI();
  }, []);

  // Show loading while detecting tenant
  if (isLoading || !tenantConfig) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          backgroundColor: '#f5f5f5'
        }}
      >
        <div style={{ textAlign: 'center' }}>
          <div>🔍 Detecting tenant...</div>
          <div style={{ fontSize: '12px', marginTop: '10px', opacity: 0.7 }}>Hostname: {window.location.hostname}</div>
        </div>
      </div>
    );
  }

  return (
    <>
      <ThemeCustomization>
        <RTLLayout>
          <Locales>
            <ScrollTop>
              <AuthProvider>
                <TenantProvider initialTenant={tenantConfig}>
                  <>
                    <RouterProvider router={router} />
                    <Snackbar />
                  </>
                </TenantProvider>
              </AuthProvider>
            </ScrollTop>
          </Locales>
        </RTLLayout>
      </ThemeCustomization>
    </>
  );
}
