// components/IconSVG.jsx
import React from 'react';

function IconSVG({ iconPath, width = 25, height = 25, color = '#1f1f1f' }) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 15 17" width={width} height={height} fill="currentColor" style={{ color }}>
      {Array.isArray(iconPath) ? iconPath.map((d, i) => <path key={i} d={d} />) : <path d={iconPath} />}
    </svg>
  );
}

export default IconSVG;
