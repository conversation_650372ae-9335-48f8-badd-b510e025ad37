import React, { useEffect, useState } from 'react';
import { useTenant } from '../contexts/TenantContext';
import { getTenantHeaderInfo } from '../services/multiTenantApi';

/**
 * Multi-Tenant Header Component
 * Automatically loads and displays tenant-specific header data from API
 */
const TenantHeader = () => {
  const { currentTenant } = useTenant();

  // Local state for API data
  const [apiHeaderInfo, setApiHeaderInfo] = useState(null);
  const [apiLoading, setApiLoading] = useState(false);
  const [apiError, setApiError] = useState(null);

  // Load header data from API
  const loadHeaderData = async () => {
    if (!currentTenant?.id) return;

    setApiLoading(true);
    setApiError(null);

    try {
      console.log('🔄 Loading header data for tenant:', currentTenant.id, currentTenant.language);
      
      const headerData = await getTenantHeaderInfo(
        currentTenant.id, 
        currentTenant.language || 'en'
      );
      
      console.log('✅ Header data loaded:', headerData);
      setApiHeaderInfo(headerData);
      
    } catch (error) {
      console.error('❌ Failed to load header data:', error);
      setApiError(error.message);
    } finally {
      setApiLoading(false);
    }
  };

  // Load API data when tenant changes
  useEffect(() => {
    if (currentTenant?.id) {
      loadHeaderData();
    }
  }, [currentTenant?.id, currentTenant?.language]);

  // Manual refresh function
  const handleRefresh = () => {
    loadHeaderData();
  };

  // Loading state
  if (apiLoading) {
    return (
      <header style={{ 
        padding: '20px', 
        textAlign: 'center', 
        backgroundColor: '#f5f5f5',
        borderBottom: '1px solid #ddd'
      }}>
        <div>🔄 Loading tenant header...</div>
      </header>
    );
  }

  // Error state
  if (apiError) {
    return (
      <header style={{ 
        padding: '20px', 
        textAlign: 'center', 
        backgroundColor: '#ffebee',
        color: '#c62828',
        borderBottom: '1px solid #ddd'
      }}>
        <div>❌ Error loading header: {apiError}</div>
        <button 
          onClick={handleRefresh}
          style={{
            marginTop: '10px',
            padding: '5px 10px',
            backgroundColor: '#c62828',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </header>
    );
  }

  // No tenant configured
  if (!currentTenant) {
    return (
      <header style={{ 
        padding: '20px', 
        textAlign: 'center', 
        backgroundColor: '#fff3cd',
        color: '#856404',
        borderBottom: '1px solid #ddd'
      }}>
        <div>⚠️ No tenant configured</div>
      </header>
    );
  }

  // Get colors from API data (priority) or fallback to defaults
  const primaryColor = apiHeaderInfo?.primarycolor || '#8c76dd';
  const secondaryColor = apiHeaderInfo?.secondarycolor || '#FFE284';
  const tertiaryColor = apiHeaderInfo?.teritorycolor || '#FF8A19';

  return (
    <header
      style={{
        backgroundColor: primaryColor,
        color: 'white',
        padding: '20px',
        borderBottom: `3px solid ${secondaryColor}`,
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        direction: currentTenant.direction || 'ltr'
      }}
    >
      <div style={{ 
        maxWidth: '1200px', 
        margin: '0 auto',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '20px'
      }}>
        
        {/* Logo and Tenant Info */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '15px' }}>
          {apiHeaderInfo?.logo && (
            <img
              src={apiHeaderInfo.logo}
              alt="Tenant Logo"
              style={{ 
                height: '50px', 
                width: 'auto',
                borderRadius: '4px'
              }}
            />
          )}
          
          <div>
            <h1 style={{ 
              margin: 0, 
              fontSize: '24px',
              fontWeight: 'bold'
            }}>
              {apiHeaderInfo?.name || currentTenant.name}
            </h1>
            
            <div style={{ 
              fontSize: '12px', 
              opacity: 0.8,
              marginTop: '4px'
            }}>
              Tenant ID: {currentTenant.id} | Language: {currentTenant.language?.toUpperCase()}
            </div>
          </div>
        </div>

        {/* API Colors Preview */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px' }}>
          <div style={{ fontSize: '12px', opacity: 0.8 }}>
            API Colors:
          </div>
          
          <div style={{ display: 'flex', gap: '5px' }}>
            <div
              style={{
                width: '20px',
                height: '20px',
                backgroundColor: primaryColor,
                borderRadius: '50%',
                border: '2px solid white',
                boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
              }}
              title={`Primary: ${primaryColor}`}
            />
            <div
              style={{
                width: '20px',
                height: '20px',
                backgroundColor: secondaryColor,
                borderRadius: '50%',
                border: '2px solid white',
                boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
              }}
              title={`Secondary: ${secondaryColor}`}
            />
            <div
              style={{
                width: '20px',
                height: '20px',
                backgroundColor: tertiaryColor,
                borderRadius: '50%',
                border: '2px solid white',
                boxShadow: '0 1px 3px rgba(0,0,0,0.3)'
              }}
              title={`Tertiary: ${tertiaryColor}`}
            />
          </div>
        </div>

        {/* Actions */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          gap: '15px',
          fontSize: '14px'
        }}>
          
          {/* Subdomain Info */}
          <div style={{ 
            backgroundColor: 'rgba(255,255,255,0.2)',
            padding: '5px 10px',
            borderRadius: '15px',
            fontSize: '12px'
          }}>
            Subdomain: {currentTenant.subdomain}
          </div>

          {/* Refresh Button */}
          <button
            onClick={handleRefresh}
            style={{
              backgroundColor: secondaryColor,
              color: '#333',
              border: 'none',
              padding: '8px 12px',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px',
              fontWeight: 'bold'
            }}
            title="Refresh API Data"
          >
            🔄 Refresh
          </button>
        </div>
      </div>

      {/* Additional API Info */}
      {apiHeaderInfo && (
        <div style={{ 
          maxWidth: '1200px', 
          margin: '15px auto 0',
          padding: '10px',
          backgroundColor: 'rgba(255,255,255,0.1)',
          borderRadius: '4px',
          fontSize: '12px'
        }}>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: '15px', justifyContent: 'center' }}>
            {apiHeaderInfo.services && apiHeaderInfo.services.length > 0 && (
              <span>🔧 Services: {apiHeaderInfo.services.length}</span>
            )}
            {apiHeaderInfo.aboutus && (
              <span>ℹ️ About Us: {apiHeaderInfo.aboutus[0]?.status === '1' ? '✅' : '❌'}</span>
            )}
            {apiHeaderInfo.faq && (
              <span>❓ FAQ: {apiHeaderInfo.faq[0]?.status === '1' ? '✅' : '❌'}</span>
            )}
            {apiHeaderInfo.contactus && (
              <span>📞 Contact: ✅</span>
            )}
            {apiHeaderInfo.expirationdate && (
              <span>📅 Expires: {new Date(apiHeaderInfo.expirationdate * 1000).toLocaleDateString()}</span>
            )}
          </div>
        </div>
      )}
    </header>
  );
};

export default TenantHeader;
