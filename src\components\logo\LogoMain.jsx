// material-ui
import { Box } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import logo from 'assets/images/logo.png';
/**
 * if you want to use image instead of <svg> uncomment following.
 *
 * import logoDark from 'assets/images/logo-dark.svg';
 * import logo from 'assets/images/logo.svg';
 *
 */

// ==============================|| LOGO SVG ||============================== //

export default function LogoMain() {
  const theme = useTheme();

  return (
    /**
     * if you want to use image instead of svg uncomment following, and comment out <svg> element.
     *
     * <img src={theme.palette.mode === ThemeMode.DARK ? logoDark : logo} alt="icon logo" width="100" />
     *
     */
    <Box sx={{ display: 'flex', justifyContent: 'center' }}>
      <img src={logo} alt="icon logo" width="60%" />
    </Box>
  );
}
