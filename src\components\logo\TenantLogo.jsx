import React from 'react';
import { useTenant } from '../../hooks/useTenant';

const TenantLogo = ({ sx, ...other }) => {
  const { tenantConfig } = useTenant();

  const getLogoSrc = () => {
    if (tenantConfig?.branding?.logo) {
      return tenantConfig.branding.logo;
    }
    return '/assets/images/default-logo.svg';
  };

  const getAltText = () => {
    if (tenantConfig?.name) {
      return `${tenantConfig.name} Logo`;
    }
    return 'Company Logo';
  };

  return (
    <img
      src={getLogoSrc()}
      alt={getAltText()}
      style={{
        height: 40,
        ...sx
      }}
      {...other}
    />
  );
};

export default TenantLogo;
