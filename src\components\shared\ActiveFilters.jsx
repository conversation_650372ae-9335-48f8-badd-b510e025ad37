import React from 'react';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import Button from '@mui/material/Button';
import { CloseSquare } from 'iconsax-react';

const ActiveFilters = ({ filters, onClearFilter, onClearAll, totalItems }) => {
  if (!filters.length) return null;

  return (
    <Stack sx={{ gap: 2 }}>
      <Typography variant="body2" color="text.secondary">
        {totalItems} items found
      </Typography>
      <Stack direction="row" sx={{ gap: 1, flexWrap: 'wrap', marginBottom: '20px', alignItems: 'center' }}>
        {filters.map((filter) => (
          <Chip
            key={filter.key}
            label={`${filter.label}: ${filter.value}`}
            variant="outlined"
            onDelete={() => onClearFilter(filter.key)}
            deleteIcon={<CloseSquare size={14} />}
            sx={{ backgroundColor: 'white', border: 'none', borderRadius: '20px ', background: '#f3f4f6' }}
          />
        ))}
        <Button variant="text" color="primary" size="small" onClick={onClearAll} sx={{ fontWeight: 500 }}>
          Clear All
        </Button>
      </Stack>
    </Stack>
  );
};

export default ActiveFilters;
