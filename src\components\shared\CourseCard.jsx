import React from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';
import LinearProgress from '@mui/material/LinearProgress';
import IconButton from 'components/@extended/IconButton';
import { More, Global, Share } from 'iconsax-react';

const CourseCard = ({ course }) => {
  // Derive isCompleted from progress, similar to CoursesTable
  const isCompleted = course.progress === 100;

  return (
    <Card sx={{ height: '100%', transition: 'all 0.2s ease-in-out', '&:hover': { boxShadow: 4 } }}>
      {/* Course Image */}
      <Box
        sx={{
          position: 'relative',
          height: 160,
          backgroundColor: '#f5f5f5',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          overflow: 'hidden'
        }}
      >
        {course.courseimage ? (
          <img src={course.courseimage} alt={course.fullname} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
        ) : (
          <Typography variant="h4" color="text.secondary">
            📚
          </Typography> // Fallback icon or background
        )}
        <IconButton
          sx={{
            position: 'absolute',
            top: 12,
            right: 12,
            backgroundColor: 'rgba(255,255,255,0.9)',
            '&:hover': { backgroundColor: 'white' }
          }}
          size="small"
        >
          <More size={14} />
        </IconButton>
      </Box>

      <CardContent sx={{ p: 3 }}>
        {/* Language and Category */}
        <Stack direction="row" sx={{ gap: 1, mb: 2 }}>
          <Chip
            icon={<Global size={12} />}
            label={course.lang}
            size="small"
            variant="outlined"
            color="primary"
            sx={{ fontSize: '0.75rem', backgroundColor: '#e3f2fd' }}
          />
          <Chip label={course.category} size="small" variant="outlined" sx={{ fontSize: '0.75rem', backgroundColor: '#f5f5f5' }} />
        </Stack>

        {/* Course Title */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5 }}>
            {course.fullname}
          </Typography>
          <Stack direction="row" sx={{ alignItems: 'center', gap: 1 }}>
            {/* Removed course.hasIcon logic as it's not in the new API payload */}
            {/* {course.hasIcon && <Typography>🏢</Typography>} */}
            <Typography
              variant="body2"
              color={'text.secondary'} // Always secondary as hasIcon is removed
              sx={{ fontWeight: 400 }} // Always 400 as hasIcon is removed
            >
              {course.format}
            </Typography>
          </Stack>
        </Box>

        {/* Progress */}
        <Box sx={{ mb: 2 }}>
          {/* Removed "Materials Completed" text as completedMaterials/totalMaterials are not directly available */}
          {/* <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            {course.completedMaterials} of {course.totalMaterials} Materials Completed
          </Typography> */}
          <LinearProgress
            variant="determinate"
            value={course.progress || 0} // Use progress, default to 0 if null
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: '#e0e0e0',
              '& .MuiLinearProgress-bar': {
                backgroundColor: '#4caf50',
                borderRadius: 4
              }
            }}
          />
        </Box>

        {/* Share Button for completed courses */}
        {isCompleted && ( // Use the derived isCompleted variable
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
            <IconButton size="small" color="secondary">
              <Share size={16} />
            </IconButton>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default CourseCard;
