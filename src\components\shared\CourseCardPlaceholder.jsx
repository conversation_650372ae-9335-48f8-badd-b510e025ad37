import React from 'react';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';

const CourseCardPlaceholder = () => {
  return (
    <Card sx={{ height: '100%', opacity: 0.5 }}>
      <Box sx={{ height: 160, backgroundColor: '#f5f5f5' }}>
        <Box
          sx={{
            height: '100%',
            backgroundImage: `repeating-linear-gradient(
              45deg,
              transparent,
              transparent 8px,
              rgba(0,0,0,0.05) 8px,
              rgba(0,0,0,0.05) 16px
            )`
          }}
        />
      </Box>
      <CardContent sx={{ p: 3 }}>
        <Stack sx={{ gap: 2 }}>
          <Stack direction="row" sx={{ gap: 1 }}>
            <Box sx={{ width: 48, height: 16, backgroundColor: '#e0e0e0', borderRadius: 1 }} />
            <Box sx={{ width: 64, height: 16, backgroundColor: '#e0e0e0', borderRadius: 1 }} />
          </Stack>
          <Box sx={{ width: 80, height: 20, backgroundColor: '#e0e0e0', borderRadius: 1 }} />
          <Box sx={{ width: '100%', height: 8, backgroundColor: '#e0e0e0', borderRadius: 1 }} />
        </Stack>
      </CardContent>
    </Card>
  );
};

export default CourseCardPlaceholder;
