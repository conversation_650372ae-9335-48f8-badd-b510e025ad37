import React from 'react';
import PropTypes, { string } from 'prop-types';

// material-ui
import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Avatar from '@mui/material/Avatar';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';

// project-imports
import IconButton from 'components/@extended/IconButton';

// assets
import { ArrowDown2, Share, Eye, CloseCircle } from 'iconsax-react';

// ==============================|| COURSES TABLE ||============================== //

export default function CoursesTable({ loading, error, filteredCourses }) {
  console.log(filteredCourses);

  return (
    <TableContainer component={Box} sx={{ backgroundColor: 'white', borderRadius: 2 }}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
              Course
              <IconButton size="small" sx={{ ml: 0.5 }}>
                <ArrowDown2 size={12} />
              </IconButton>
            </TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>Language</TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>Category</TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
              Enrollment Date
              <IconButton size="small" sx={{ ml: 0.5 }}>
                <ArrowDown2 size={12} />
              </IconButton>
            </TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
              Last Access
              <IconButton size="small" sx={{ ml: 0.5 }}>
                <ArrowDown2 size={12} />
              </IconButton>
            </TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
              Materials
              <IconButton size="small" sx={{ ml: 0.5 }}>
                <ArrowDown2 size={12} />
              </IconButton>
            </TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>
              Completion
              <IconButton size="small" sx={{ ml: 0.5 }}>
                <ArrowDown2 size={12} />
              </IconButton>
            </TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>Status</TableCell>
            <TableCell sx={{ fontWeight: 600, color: 'text.secondary', fontSize: '0.875rem' }}>Action</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={9} align="center">
                Loading courses...
              </TableCell>
            </TableRow>
          ) : error ? (
            <TableRow>
              <TableCell colSpan={9} align="center" sx={{ color: 'error.main' }}>
                {error}
              </TableCell>
            </TableRow>
          ) : filteredCourses.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} align="center">
                No courses found.
              </TableCell>
            </TableRow>
          ) : (
            filteredCourses.map((course) => (
              <TableRow key={course.id} sx={{ '&:hover': { backgroundColor: '#f5f5f5' } }}>
                <TableCell>
                  <Stack direction="row" spacing={2} alignItems="center">
                    <Avatar
                      sx={{
                        width: 40,
                        height: 40,
                        backgroundColor: '#f5f5f5',
                        border: '1px solid #e0e0e0'
                      }}
                    >
                      {course.courseimage ? (
                        <img src={course.courseimage} alt={course.fullname} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                      ) : (
                        '📚'
                      )}
                    </Avatar>
                    <Box>
                      <Typography variant="body2" sx={{ fontWeight: 600, color: 'text.primary' }}>
                        {course.fullname}
                      </Typography>
                    </Box>
                  </Stack>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.primary">
                    {course.lang}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.primary">
                    {course.category}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
                    {course.startdate ? new Date(course.startdate * 1000).toLocaleDateString() : 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
                    {'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.primary">
                    {'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.primary" sx={{ fontWeight: 600 }}>
                    {course.progress !== null ? `${course.progress}%` : 'N/A'}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={course.progress === 100 ? 'Completed' : 'In Progress'}
                    size="small"
                    color={course.progress === 100 ? 'success' : 'warning'}
                    variant="outlined"
                    sx={{
                      backgroundColor: course.progress === 100 ? '#e8f5e8' : '#fff3e0',
                      fontWeight: 500,
                      fontSize: '0.75rem'
                    }}
                  />
                </TableCell>
                <TableCell>
                  <Stack direction="row" spacing={0.5}>
                    {course.progress === 100 && (
                      <IconButton size="small" color="primary">
                        <Share size={16} />
                      </IconButton>
                    )}
                    <IconButton size="small" color="primary">
                      <Eye size={16} />
                    </IconButton>
                    <IconButton size="small" color="error">
                      <CloseCircle size={16} />
                    </IconButton>
                  </Stack>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );
}

CoursesTable.propTypes = {
  loading: PropTypes.bool.isRequired,
  error: PropTypes.string,
  filteredCourses: PropTypes.array.isRequired
};
