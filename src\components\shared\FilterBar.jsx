import React from 'react';
import Button from '@mui/material/Button';
import Stack from '@mui/material/Stack';
import IconButton from 'components/@extended/IconButton';
import { Filter, ArrowDown2, Calendar } from 'iconsax-react';

const FilterBar = ({ onFilterClick }) => {
  const filterButtons = [
    { label: 'Provider', icon: <ArrowDown2 size={14} /> },
    { label: 'Category', icon: <ArrowDown2 size={14} /> },
    { label: 'Language', icon: <ArrowDown2 size={14} /> },
    { label: 'Status', icon: <ArrowDown2 size={14} /> },
    { label: 'Enrollment Date', icon: <Calendar size={16} /> }
  ];

  return (
    <Stack direction="row" sx={{ gap: 2, marginBottom: '20px', flexWrap: 'wrap', alignItems: 'center' }}>
      <IconButton color="secondary" sx={{ border: '1px solid #e0e0e0' }} onClick={() => onFilterClick('filter')}>
        <Filter size={16} />
      </IconButton>

      {filterButtons.map((button, index) => (
        <Button
          key={index}
          variant="outlined"
          color="inherit"
          endIcon={button.icon}
          onClick={() => onFilterClick(button.label.toLowerCase())}
          sx={{ color: 'text.primary', border: 'none', borderRadius: '20px ', background: '#f3f4f6' }}
        >
          {button.label}
        </Button>
      ))}
    </Stack>
  );
};

export default FilterBar;
