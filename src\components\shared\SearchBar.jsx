import React from 'react';
import TextField from '@mui/material/TextField';
import InputAdornment from '@mui/material/InputAdornment';
import { SearchNormal1 } from 'iconsax-react';
import { FormattedMessage, useIntl } from 'react-intl';

const SearchBar = ({ value, onChange, placeholder = 'Search', fullWidth = true }) => {
  const intl = useIntl();
  return (
    <TextField
      fullWidth={fullWidth}
      placeholder={intl.formatMessage({ id: 'search' })}
      value={value}
      onChange={onChange}
      InputProps={{
        startAdornment: (
          <InputAdornment position="start">
            <SearchNormal1 size={20} />
          </InputAdornment>
        ),
        sx: {
          backgroundColor: '#f3f4f6',
          borderRadius: 2,
          '& .MuiOutlinedInput-notchedOutline': {
            border: 'none'
          },
          '&:hover .MuiOutlinedInput-notchedOutline': {
            border: 'none'
          },
          '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
            border: '2px solid #1976d2'
          }
        }
      }}
    />
  );
};

export default SearchBar;
