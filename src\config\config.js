// API Configuration
export const API_BASE_URL = 'https://d76827576f87.ngrok-free.app';

// Multi-Tenant Moodle Configuration
export const MOODLE_CONFIG = {
  // Base URL for Moodle Web Services
  BASE_URL: '/webservice/rest/server.php',

  DEFAULT_WS_TOKEN: '5e2b2559f3a4052a78c062cae22948bb',

  // Moodle Web Service Functions
  FUNCTIONS: {
    GET_TENANT_INFO: 'local_guestapi_get_tenant_info',
    GET_TENANT_HEADER_INFO: 'local_guestapi_get_tenant_header_info',
    GET_FOOTER_INFO: 'local_guestapi_get_footer',
    GET_PROGRAMS: 'local_guestapi_get_available_programs',
    GET_CLASSROOMS: 'local_guestapi_get_available_classes',
    GET_COURSES: 'local_guestapi_get_available_courses'
  },

  // Build full Moodle API URL
  getFullUrl: () => `${API_BASE_URL}${MOODLE_CONFIG.BASE_URL}`
};

// API Endpoints
export const API_ENDPOINTS = {
  // Auth
  LOGIN: '/api/login',
  LOGOUT: '/api/logout',

  // User
  USER_PROFILE: '/api/user/profile',

  // Courses
  ENROLLED_COURSES: '/api/courses/enrolled',

  // Moodle Multi-Tenant
  MOODLE_WS: MOODLE_CONFIG.BASE_URL
};
