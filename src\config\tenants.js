import { API_BASE_URL, MOODLE_CONFIG } from './config';

/**
 * Multi-Tenant Configuration using subdomain mapping
 * Maps subdomains to tenant IDs and configurations
 */
export const tenants = {
  // Tenant 1 - Arabic focused
  tenant1: {
    id: 83,
    name: 'المؤسسة التعليمية الأولى',
    subdomain: 'tenant1',
    language: 'ar',
    direction: 'rtl'
  },

  // Tenant 2 - English focused
  tenant2: {
    id: 84,
    name: 'Educational Institution Two',
    subdomain: 'tenant2',
    language: 'en',
    direction: 'ltr'
  },

  // Tenant 3 - Another example
  tenant3: {
    id: 85,
    name: 'Tech Academy',
    subdomain: 'tenant3',
    language: 'en',
    direction: 'ltr'
  },

  // Default tenant for fallback
  default: {
    id: 83, // Default to tenant 83
    name: 'Default Institution',
    subdomain: 'default',
    language: 'en',
    direction: 'ltr'
  }
};

/**
 * Get tenant configuration by subdomain
 * @param {string} subdomain - The subdomain to look up
 * @returns {Object} Tenant configuration object with API settings
 */
export const getTenantBySubdomain = (subdomain) => {
  const tenant = tenants[subdomain] || tenants.default;

  // Add API configuration to tenant
  return {
    ...tenant,
    apiEndpoint: API_BASE_URL,
    moodleConfig: {
      baseUrl: MOODLE_CONFIG.BASE_URL,
      wsToken: MOODLE_CONFIG.DEFAULT_WS_TOKEN,
      functions: MOODLE_CONFIG.FUNCTIONS
    }
  };
};

/**
 * Extract subdomain from hostname
 * @param {string} hostname - The hostname to extract subdomain from
 * @returns {string} The subdomain or 'default'
 */
export const extractSubdomain = (hostname) => {
  // Handle localhost development
  if (hostname === 'localhost' || hostname.startsWith('127.0.0.1')) {
    return 'default';
  }

  const parts = hostname.split('.');

  // If it's a subdomain (e.g., tenant1.example.com)
  if (parts.length >= 3) {
    return parts[0];
  }

  // Default fallback
  return 'default';
};

/**
 * Get tenant by ID
 * @param {number} tenantId - The tenant ID to look up
 * @returns {Object} Tenant configuration object
 */
export const getTenantById = (tenantId) => {
  const tenant = Object.values(tenants).find((t) => t.id === tenantId) || tenants.default;

  return {
    ...tenant,
    apiEndpoint: API_BASE_URL,
    moodleConfig: {
      baseUrl: MOODLE_CONFIG.BASE_URL,
      wsToken: MOODLE_CONFIG.DEFAULT_WS_TOKEN,
      functions: MOODLE_CONFIG.FUNCTIONS
    }
  };
};

export default tenants;
