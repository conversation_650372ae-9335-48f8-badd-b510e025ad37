import React, { createContext, useEffect, useReducer } from 'react';

// third-party
import { Chance } from 'chance';
import { jwtDecode } from 'jwt-decode';

// reducer - state management
import { LOGIN, LOGOUT } from 'contexts/auth-reducer/actions';
import authReducer from 'contexts/auth-reducer/auth';

// project-imports
import Loader from 'components/Loader';
import axios from 'utils/axios';
import { decryptPayload } from 'utils/decryption';
import { API_BASE_URL, API_ENDPOINTS } from '../config/config';

const chance = new Chance();

// constant
const initialState = {
  isLoggedIn: false,
  isInitialized: false,
  user: null
};

const verifyToken = (serviceToken) => {
  if (!serviceToken) {
    return false;
  }
  const decoded = jwtDecode(serviceToken);
  /**
   * Property 'exp' does not exist on type '<T = unknown>(token: string, options?: JwtDecodeOptions | undefined) => T'.
   */
  return decoded.exp > Date.now() / 1000;
};

const setSession = (serviceToken) => {
  if (serviceToken) {
    localStorage.setItem('serviceToken', serviceToken);
    axios.defaults.headers.common.Authorization = `Bearer ${serviceToken}`;
  } else {
    localStorage.removeItem('serviceToken');
    delete axios.defaults.headers.common.Authorization;
  }
};

// ==============================|| JWT CONTEXT & PROVIDER ||============================== //

const JWTContext = createContext(null);

export const JWTProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  useEffect(() => {
    const init = async () => {
      try {
        const serviceToken = window.localStorage.getItem('serviceToken');
        if (serviceToken) {
          setSession(serviceToken);
          // Since we don't have a /me endpoint, we'll create a user object from localStorage
          const userData = JSON.parse(localStorage.getItem('userData') || '{}');
          dispatch({
            type: LOGIN,
            payload: {
              isLoggedIn: true,
              user: userData
            }
          });
        } else {
          dispatch({
            type: LOGOUT
          });
        }
      } catch (err) {
        console.error(err);
        dispatch({
          type: LOGOUT
        });
      }
    };

    init();
  }, []);

  const login = async (username, password) => {
    try {
      const response = await axios.post(`${API_BASE_URL}${API_ENDPOINTS.LOGIN}`, {
        username,
        password
      });

      console.log('Raw response:', response);

      if (!response?.data) {
        throw new Error('No data received from server');
      }

      const responseData = response.data;

      if (responseData.encrypted && responseData.payload) {
        if (!responseData.payload.iv || !responseData.payload.data) {
          throw new Error('Invalid encrypted payload structure');
        }

        const decryptedData = decryptPayload(responseData.payload.data, responseData.payload.iv);
        const parsedData = JSON.parse(decryptedData);

        if (!parsedData.access_token) {
          throw new Error('Decrypted data missing access token');
        }

        setSession(parsedData.access_token);

        const user = {
          id: parsedData.user_id || parsedData.id,
          name: parsedData.fullname || parsedData.name
        };

        localStorage.setItem('userData', JSON.stringify(user));
        dispatch({
          type: LOGIN,
          payload: {
            isLoggedIn: true,
            user
          }
        });
        return;
      }

      if (responseData.data && responseData.data.access_token) {
        setSession(responseData.data.access_token);

        const user = {
          id: responseData.data.user_id || responseData.data.id,
          name: responseData.data.fullname || responseData.data.name
        };

        localStorage.setItem('userData', JSON.stringify(user));
        dispatch({
          type: LOGIN,
          payload: {
            isLoggedIn: true,
            user
          }
        });
        return;
      }

      // حالة الاستجابة غير المعروفة
      throw new Error('Unexpected response format: ' + JSON.stringify(responseData));
    } catch (error) {
      console.error('Login error:', error);

      // يمكنك إضافة معالجة خاصة لأخطاء فك التشفير
      if (error.message.includes('decryption')) {
        dispatch({
          type: LOGOUT,
          payload: {
            error: 'Decryption failed. Please try again.'
          }
        });
      } else {
        dispatch({
          type: LOGOUT,
          payload: {
            error: error.message || 'Login failed'
          }
        });
      }

      throw error; // لإعادة التصعيد للعناصر التي تستدعي هذه الدالة
    }
  };

  const register = async (email, password, firstName, lastName) => {
    const response = await axios.post(`${API_BASE_URL}${API_ENDPOINTS.REGISTER}`, {
      email,
      password,
      firstName,
      lastName
    });
    return response.data;
  };

  const logout = () => {
    setSession(null);
    localStorage.removeItem('userData');
    dispatch({ type: LOGOUT });
  };

  const resetPassword = async (email) => {
    console.log('email - ', email);
  };

  const updateProfile = () => {};

  if (state.isInitialized !== undefined && !state.isInitialized) {
    return <Loader />;
  }

  return <JWTContext.Provider value={{ ...state, login, logout, register, resetPassword, updateProfile }}>{children}</JWTContext.Provider>;
};

export default JWTContext;
