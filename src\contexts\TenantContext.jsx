// src/contexts/TenantContext.jsx
import React, { createContext, useContext, useState, useEffect } from 'react';
import { getTenantHeaderInfo, getTenantFooterInfo } from '../services/multiTenantApi';

const TenantContext = createContext();

// Provider Component
export const TenantProvider = ({ children, initialTenant = null }) => {
  const [currentTenant, setCurrentTenant] = useState(null);
  const [tenantHeaderInfo, setTenantHeaderInfo] = useState(null);
  const [tenantFooterInfo, setTenantFooterInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    let isMounted = true;
    const loadTenant = async () => {
      try {
        // If initialTenant is provided (from subdomain detection), use it
        if (initialTenant && isMounted) {
          setCurrentTenant(initialTenant);

          // Auto-load header and footer info
          try {
            const [headerInfo, footerInfo] = await Promise.allSettled([
              getTenantHeaderInfo(initialTenant.id, initialTenant.language),
              getTenantFooterInfo(initialTenant.id, initialTenant.language)
            ]);

            if (isMounted) {
              if (headerInfo.status === 'fulfilled') {
                setTenantHeaderInfo(headerInfo.value);
              }
              if (footerInfo.status === 'fulfilled') {
                setTenantFooterInfo(footerInfo.value);
              }
            }
          } catch (apiErr) {
            if (isMounted) {
              setError(apiErr.message);
            }
          }
        } else {
          // Fallback to localStorage if no initialTenant
          const savedTenant = localStorage.getItem('currentTenant');
          if (savedTenant && isMounted) {
            const tenantData = JSON.parse(savedTenant);
            setCurrentTenant(tenantData);
          }
        }
      } catch (err) {
        if (isMounted) {
          setError(err.message);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadTenant();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, [initialTenant]);

  const setTenant = (tenant) => {
    try {
      if (!tenant) {
        clearTenant();
        return;
      }

      setCurrentTenant(tenant);
      localStorage.setItem('currentTenant', JSON.stringify(tenant));
      setError(null);
    } catch (err) {
      console.error('Error setting tenant:', err);
      setError(err.message);
    }
  };

  const clearTenant = () => {
    setCurrentTenant(null);
    localStorage.removeItem('currentTenant');
    setError(null);
  };

  const switchTenant = (tenantData) => {
    try {
      if (tenantData) {
        setTenant(tenantData);
        // Clear cached Moodle data when switching tenants
        setTenantHeaderInfo(null);
        setTenantFooterInfo(null);
      } else {
        throw new Error('Tenant data is required');
      }
    } catch (err) {
      console.error('Error switching tenant:', err);
      setError(err.message);
    }
  };

  /**
   * Load tenant header info from Moodle API
   */
  const loadTenantHeaderInfo = async (lang = null) => {
    if (!currentTenant?.id) return null;

    try {
      const language = lang || currentTenant.language || 'en';
      const headerInfo = await getTenantHeaderInfo(currentTenant.id, language);
      setTenantHeaderInfo(headerInfo);
      return headerInfo;
    } catch (err) {
      console.error('Failed to load tenant header info:', err);
      setError(err.message);
      return null;
    }
  };

  /**
   * Load tenant footer info from Moodle API
   */
  const loadTenantFooterInfo = async (lang = null) => {
    if (!currentTenant?.id) return null;

    try {
      const language = lang || currentTenant.language || 'en';
      const footerInfo = await getTenantFooterInfo(currentTenant.id, language);
      setTenantFooterInfo(footerInfo);
      return footerInfo;
    } catch (err) {
      console.error('Failed to load tenant footer info:', err);
      setError(err.message);
      return null;
    }
  };

  return (
    <TenantContext.Provider
      value={{
        currentTenant,
        tenantHeaderInfo,
        tenantFooterInfo,
        setTenant,
        clearTenant,
        switchTenant,
        loadTenantHeaderInfo,
        loadTenantFooterInfo,
        isLoading,
        error
      }}
    >
      {children}
    </TenantContext.Provider>
  );
};

export const useTenant = () => {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
};

export default TenantContext;
