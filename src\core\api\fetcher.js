// fetcher.js
import axios from 'axios';
import { API_BASE_URL } from '../../config/config';
import { getTenantId } from '../../utils/tenant';

// Create an Axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'ngrok-skip-browser-warning': 'true'
  }
});

// Interceptor to add Authorization token dynamically
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('serviceToken');

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    config.headers['ngrok-skip-browser-warning'] = 'true';

    const tenantId = getTenantId();
    config.headers['X-Tenant-Id'] = tenantId;

    return config;
  },
  (error) => Promise.reject(error)
);

// Handle Response
const handleResponse = (response) => {
  if (response.status >= 200 && response.status < 300) {
    return { success: true, data: response.data, message: null };
  } else if (response.status === 401) {
    localStorage.removeItem('serviceToken');
    return { success: false, data: null, message: 'Unauthorized. Please log in again.' };
  } else if (response.status === 500) {
    return { success: false, data: null, message: 'Internal server error. Please try again later.' };
  } else {
    return { success: false, data: null, message: `Failed with status: ${response.status}` };
  }
};

const handleError = (error) => {
  console.error('API error:', error);

  if (error.response) {
    const status = error.response.status;
    const data = error.response.data;

    switch (status) {
      case 401:
        localStorage.removeItem('serviceToken');
        return { success: false, data: null, message: 'Unauthorized. Please log in again.' };
      case 403:
        return { success: false, data: null, message: 'Access forbidden.' };
      case 404:
        return { success: false, data: null, message: 'Resource not found.' };
      case 500:
        return { success: false, data: null, message: 'Internal server error. Please try again later.' };
      default:
        return {
          success: false,
          data: null,
          message: data?.message || data?.error || `Error: ${status}`
        };
    }
  } else if (error.request) {
    // Network error
    return { success: false, data: null, message: 'Network error. Please check your connection.' };
  } else {
    return { success: false, data: null, message: 'An unexpected error occurred.' };
  }
};

const getRequest = async (endpoint) => {
  try {
    const response = await api.get(endpoint);
    return handleResponse(response);
  } catch (error) {
    return handleError(error);
  }
};

const postRequest = async (endpoint, data, isFileUpload = false) => {
  try {
    const response = await api.post(endpoint, data);
    return handleResponse(response);
  } catch (error) {
    return handleError(error);
  }
};

const putRequest = async (endpoint, data) => {
  try {
    const response = await api.put(endpoint, data);
    return handleResponse(response);
  } catch (error) {
    return handleError(error);
  }
};

const deleteRequest = async (endpoint, id) => {
  try {
    const response = await api.delete(endpoint, id);
    return handleResponse(response);
  } catch (error) {
    return handleError(error);
  }
};

export { getRequest, postRequest, putRequest, deleteRequest };
