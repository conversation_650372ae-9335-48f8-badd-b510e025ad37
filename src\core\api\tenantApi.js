import axios from 'axios';

export const tenantApi = {
  getTenantByDomain: async (domain) => {
    try {
      const response = await axios.get(`/api/tenants/domain/${domain}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch tenant information');
    }
  },

  getTenantById: async (tenantId) => {
    try {
      const response = await axios.get(`/api/tenants/${tenantId}`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch tenant information');
    }
  },

  getTenantSettings: async (tenantId) => {
    try {
      const response = await axios.get(`/api/tenants/${tenantId}/settings`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch tenant settings');
    }
  },

  updateTenantSettings: async (tenantId, settings) => {
    try {
      const response = await axios.put(`/api/tenants/${tenantId}/settings`, settings);
      return response.data;
    } catch (error) {
      throw new Error('Failed to update tenant settings');
    }
  },

  // Fetch courses for a specific tenant
  getTenantCourses: async (tenantId) => {
    try {
      const response = await axios.get(`/api/tenants/${tenantId}/courses`);
      return response.data;
    } catch (error) {
      throw new Error('Failed to fetch courses');
    }
  }
};
