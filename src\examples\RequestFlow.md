# 🔄 **Request Flow - كيف يتم استخدام getTenantInfo في كل Request**

## 📋 **الوضع الحالي**

### **1. App.jsx - Initial Load**
```javascript
// App.jsx
useEffect(() => {
  const loadTenant = async () => {
    const hostname = window.location.hostname;
    
    // 🔥 استدعاء getTenantInfo مرة واحدة في البداية
    const tenantData = await getTenantInfo(hostname, null, 'ar');
    
    // النتيجة: { id: 83, name: "تست", primarycolor: "#032E9B", ... }
    setTenantConfig(tenantData);
  };
  
  loadTenant();
}, []);

// تمرير البيانات للـ TenantProvider
<TenantProvider initialTenant={tenantConfig}>
```

### **2. TenantContext - State Management**
```javascript
// TenantContext.jsx
const [currentTenant, setCurrentTenant] = useState(null);

useEffect(() => {
  if (initialTenant) {
    setCurrentTenant(initialTenant); // حفظ البيانات
    
    // استخدام tenant.id في requests إضافية
    loadTenantHeaderInfo(initialTenant.id, initialTenant.language);
    loadTenantFooterInfo(initialTenant.id, initialTenant.language);
  }
}, [initialTenant]);
```

### **3. Components - Using Tenant ID**
```javascript
// Programs.jsx
const { currentTenant } = useTenant();

useEffect(() => {
  if (currentTenant?.id) {
    // 🎯 استخدام currentTenant.id (الذي جاء من getTenantInfo)
    const response = await getTenantPrograms(currentTenant.id, 'ar');
    setPrograms(response.programs);
  }
}, [currentTenant?.id]);

// CoursesSection.jsx
const { currentTenant } = useTenant();

useEffect(() => {
  if (currentTenant?.id) {
    // 🎯 استخدام currentTenant.id (الذي جاء من getTenantInfo)
    const response = await getTenantCourses(currentTenant.id, 'ar', 10);
    setCourses(response.courses);
  }
}, [currentTenant?.id]);
```

## 🔄 **البديل: استدعاء getTenantInfo في كل Request**

إذا كنت تريد استدعاء `getTenantInfo` في كل request، يمكننا تعديل النظام:

### **Option 1: Wrapper Function**
```javascript
// src/services/tenantApiWrapper.js
import { getTenantInfo, getTenantPrograms, getTenantCourses } from './multiTenantApi';

// Cache للـ tenant info
let cachedTenantInfo = null;
let cacheTimestamp = null;
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

async function ensureTenantInfo() {
  const now = Date.now();
  
  // Check if cache is valid
  if (cachedTenantInfo && cacheTimestamp && (now - cacheTimestamp) < CACHE_DURATION) {
    return cachedTenantInfo;
  }
  
  // Get fresh tenant info
  const hostname = window.location.hostname;
  cachedTenantInfo = await getTenantInfo(hostname, null, 'ar');
  cacheTimestamp = now;
  
  return cachedTenantInfo;
}

// Wrapper functions that always get fresh tenant info
export async function getPrograms(lang = 'ar') {
  const tenantInfo = await ensureTenantInfo();
  return await getTenantPrograms(tenantInfo.id, lang);
}

export async function getCourses(lang = 'ar', maxResults = null) {
  const tenantInfo = await ensureTenantInfo();
  return await getTenantCourses(tenantInfo.id, lang, maxResults);
}

export async function getClassrooms(lang = 'ar') {
  const tenantInfo = await ensureTenantInfo();
  return await getTenantClassrooms(tenantInfo.id, lang);
}
```

### **Option 2: Enhanced API Service**
```javascript
// src/services/enhancedTenantApi.js
class EnhancedTenantApiService {
  constructor() {
    this.tenantCache = null;
    this.cacheTimestamp = null;
    this.cacheDuration = 5 * 60 * 1000; // 5 minutes
  }

  async getTenantInfo(forceRefresh = false) {
    const now = Date.now();
    
    if (!forceRefresh && this.tenantCache && 
        this.cacheTimestamp && 
        (now - this.cacheTimestamp) < this.cacheDuration) {
      return this.tenantCache;
    }

    const hostname = window.location.hostname;
    this.tenantCache = await getTenantInfo(hostname, null, 'ar');
    this.cacheTimestamp = now;
    
    return this.tenantCache;
  }

  async getPrograms(lang = 'ar') {
    const tenantInfo = await this.getTenantInfo();
    return await getTenantPrograms(tenantInfo.id, lang);
  }

  async getCourses(lang = 'ar', maxResults = null) {
    const tenantInfo = await this.getTenantInfo();
    return await getTenantCourses(tenantInfo.id, lang, maxResults);
  }

  async getClassrooms(lang = 'ar') {
    const tenantInfo = await this.getTenantInfo();
    return await getTenantClassrooms(tenantInfo.id, lang);
  }
}

export const enhancedTenantApi = new EnhancedTenantApiService();
```

### **Option 3: Hook with Auto-Refresh**
```javascript
// src/hooks/useAutoTenantData.js
import { useState, useEffect, useCallback } from 'react';
import { getTenantInfo, getTenantPrograms } from '../services/multiTenantApi';

export const useAutoTenantData = () => {
  const [tenantInfo, setTenantInfo] = useState(null);
  const [programs, setPrograms] = useState([]);
  const [loading, setLoading] = useState(false);

  const refreshTenantInfo = useCallback(async () => {
    const hostname = window.location.hostname;
    const info = await getTenantInfo(hostname, null, 'ar');
    setTenantInfo(info);
    return info;
  }, []);

  const loadPrograms = useCallback(async (lang = 'ar') => {
    setLoading(true);
    try {
      // Always get fresh tenant info
      const info = await refreshTenantInfo();
      const response = await getTenantPrograms(info.id, lang);
      setPrograms(response.programs);
    } finally {
      setLoading(false);
    }
  }, [refreshTenantInfo]);

  return {
    tenantInfo,
    programs,
    loading,
    loadPrograms,
    refreshTenantInfo
  };
};
```

## 🎯 **التوصية**

### **الوضع الحالي جيد لأنه:**
- ✅ **Performance** - استدعاء واحد في البداية
- ✅ **Caching** - البيانات محفوظة في Context
- ✅ **Consistency** - نفس البيانات في كل التطبيق
- ✅ **Simple** - سهل الفهم والصيانة

### **إذا كنت تريد Fresh Data في كل Request:**
- 🔄 استخدم **Option 1** (Wrapper Functions) مع caching
- 🔄 أو **Option 2** (Enhanced Service) للتحكم الكامل
- 🔄 أو **Option 3** (Auto-Refresh Hook) للمكونات المحددة

## 📊 **مقارنة الطرق**

| الطريقة | Performance | Freshness | Complexity | Use Case |
|---------|-------------|-----------|------------|----------|
| **Current** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | معظم التطبيقات |
| **Wrapper** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | بيانات متغيرة |
| **Enhanced** | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | تحكم كامل |
| **Auto-Hook** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | مكونات محددة |

**أي طريقة تفضل؟** 🤔
