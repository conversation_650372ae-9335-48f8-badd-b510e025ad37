import React from 'react';
import TenantHeader from '../components/TenantHeader';

/**
 * Simple example showing how to use the Multi-Tenant Header
 */
const SimpleHeaderExample = () => {
  return (
    <div>
      {/* Multi-Tenant Header - automatically detects tenant from subdomain */}
      <TenantHeader />
      
      {/* Your page content goes here */}
      <main style={{ padding: '20px' }}>
        <h1>Welcome to Multi-Tenant Application</h1>
        <p>The header above automatically changes based on the subdomain:</p>
        
        <ul>
          <li><strong>tenant1.yourdomain.com</strong> - Shows Arabic tenant</li>
          <li><strong>tenant2.yourdomain.com</strong> - Shows English tenant</li>
          <li><strong>yourdomain.com</strong> - Shows default tenant</li>
        </ul>
        
        <p>Current URL: <code>{window.location.href}</code></p>
      </main>
    </div>
  );
};

export default SimpleHeaderExample;
