# 🏗️ **Tenant Data Flow - كي<PERSON> يعمل النظام**

## 📋 **التدفق الكامل للبيانات**

### **1. App.jsx - نقطة البداية**
```javascript
// App.jsx
const hostname = window.location.hostname; // example: "tenant1.naahel.com"

// 🔥 هنا يتم استدعاء getTenantInfo للحصول على معلومات Tenant
const tenantData = await getTenantInfo(hostname, null, 'ar');

// النتيجة:
{
  "id": 83,                    // ← هذا هو الـ ID المطلوب لكل التطبيق
  "name": "تست",
  "primarycolor": "#032E9B",
  "secondarycolor": "#FFE284",
  "teritorycolor": "#FF8A19",
  "services": [...],
  "logo": "http://..."
}

// تمرير البيانات للـ TenantProvider
<TenantProvider initialTenant={tenantData}>
```

### **2. TenantContext.jsx - إدارة الحالة**
```javascript
// TenantContext.jsx
export const TenantProvider = ({ children, initialTenant }) => {
  const [currentTenant, setCurrentTenant] = useState(null);

  useEffect(() => {
    if (initialTenant) {
      setCurrentTenant(initialTenant); // ← حفظ البيانات في Context
      
      // تحميل بيانات إضافية
      loadTenantHeaderInfo(initialTenant.id, initialTenant.language);
      loadTenantFooterInfo(initialTenant.id, initialTenant.language);
    }
  }, [initialTenant]);

  // توفير البيانات لكل التطبيق
  return (
    <TenantContext.Provider value={{ currentTenant, ... }}>
      {children}
    </TenantContext.Provider>
  );
};
```

### **3. استخدام البيانات في المكونات**

#### **أ. Programs.jsx**
```javascript
// Programs.jsx
const { currentTenant } = useTenant();

useEffect(() => {
  if (currentTenant?.id) {
    // 🎯 استخدام currentTenant.id لجلب البرامج
    const response = await getTenantPrograms(currentTenant.id, 'ar');
    setPrograms(response.programs);
  }
}, [currentTenant?.id]);
```

#### **ب. CoursesSection.jsx**
```javascript
// CoursesSection.jsx
const { currentTenant } = useTenant();

useEffect(() => {
  if (currentTenant?.id) {
    // 🎯 استخدام currentTenant.id لجلب الكورسات
    const response = await getTenantCourses(currentTenant.id, 'ar', 10);
    setCourses(response.courses);
    setCategories(response.categories);
  }
}, [currentTenant?.id]);
```

#### **ج. Header.jsx**
```javascript
// Header.jsx
const { currentTenant } = useTenant();

// 🎨 استخدام الألوان من currentTenant
const colors = {
  primary: currentTenant?.primaryColor || '#8c76dd',
  secondary: currentTenant?.secondaryColor || '#FFE284',
  tertiary: currentTenant?.teritoryColor || '#FF8A19'
};

// 🖼️ استخدام الشعار من currentTenant
<img src={currentTenant?.logo || defaultLogo} />
```

## 🔄 **APIs المتاحة**

### **1. getTenantInfo** - الأساسية
```javascript
// Function: local_guestapi_get_tenant_info
// Parameters: url, code (optional), lang
// Returns: { id, name, colors, services, logo, ... }

const tenantData = await getTenantInfo('tenant1.naahel.com', null, 'ar');
```

### **2. getTenantPrograms** - البرامج
```javascript
// Function: local_guestapi_get_available_programs  
// Parameters: tenantid, lang
// Returns: { programs: [...], total, ... }

const programsData = await getTenantPrograms(83, 'ar');
```

### **3. getTenantCourses** - الكورسات
```javascript
// Function: local_guestapi_get_available_courses
// Parameters: tenantid, lang, maxresults (optional)
// Returns: { courses: [...], categories: [...], total, ... }

const coursesData = await getTenantCourses(83, 'ar', 10);
```

### **4. getTenantClassrooms** - الفصول
```javascript
// Function: local_guestapi_get_available_classes
// Parameters: tenantid, lang
// Returns: { classrooms: [...], total, ... }

const classroomsData = await getTenantClassrooms(83, 'ar');
```

### **5. getTenantHeaderInfo** - معلومات الهيدر
```javascript
// Function: local_guestapi_get_tenant_header_info
// Parameters: tenantid, lang
// Returns: { name, logo, ... }

const headerInfo = await getTenantHeaderInfo(83, 'ar');
```

### **6. getTenantFooterInfo** - معلومات الفوتر
```javascript
// Function: local_guestapi_get_footer
// Parameters: tenantid, lang
// Returns: { footer data }

const footerInfo = await getTenantFooterInfo(83, 'ar');
```

## 🎯 **المميزات الرئيسية**

### ✅ **Dynamic Multi-Tenancy**
- كل tenant له بياناته الخاصة
- الألوان والشعارات ديناميكية
- المحتوى يتغير حسب الـ tenant

### ✅ **Centralized State Management**
- TenantContext يدير كل البيانات
- useTenant hook للوصول السهل
- Real-time updates عند تغيير tenant

### ✅ **Error Handling & Fallbacks**
- Loading states في كل مكون
- Fallback data في حالة الأخطاء
- User-friendly error messages

### ✅ **Performance Optimization**
- API caching في multiTenantApi
- Lazy loading للبيانات
- Efficient re-renders

## 🚀 **الاستخدام العملي**

```javascript
// في أي مكون
import { useTenant } from '../contexts/TenantContext';

const MyComponent = () => {
  const { currentTenant } = useTenant();
  
  // استخدام البيانات
  const tenantId = currentTenant?.id;
  const tenantName = currentTenant?.name;
  const primaryColor = currentTenant?.primaryColor;
  const logo = currentTenant?.logo;
  
  return (
    <div style={{ color: primaryColor }}>
      <img src={logo} alt={tenantName} />
      <h1>Welcome to {tenantName}</h1>
    </div>
  );
};
```

**النظام الآن يعمل بكفاءة عالية ويدعم Multi-Tenancy بالكامل! 🎉**
