// material-ui
import { styled } from '@mui/material/styles';
import Box from '@mui/material/Box';

// ==============================|| DRAWER HEADER - STYLED ||============================== //

const DrawerHeaderStyled = styled(Box, { shouldForwardProp: (prop) => prop !== 'open' })(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  paddingLeft: theme.spacing(0),
  variants: [
    {
      props: ({ open }) => open,
      style: {
        justifyContent: 'flex-start',
        padding: theme.spacing(2)
      }
    }
  ]
}));

export default DrawerHeaderStyled;
