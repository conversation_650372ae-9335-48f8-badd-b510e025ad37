import PropTypes from 'prop-types';
// material-ui
import { styled } from '@mui/material/styles';
import Container from '@mui/material/Container';
import Grid from '@mui/material/Grid2';
import Link from '@mui/material/Link';
import Stack from '@mui/material/Stack';
import Tooltip from '@mui/material/Tooltip';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import { AppBar, Toolbar, Button, IconButton, Menu, MenuItem, alpha, Divider } from '@mui/material';
import logo from 'assets/images/naheel-logo-02.png';
import logoFooter from 'assets/images/dolf-logo.svg';
// third-party
import { motion } from 'framer-motion';

// project-imports
import Logo from 'components/logo';

// assets
import { Dribbble, Youtube, Instagram, Global, Call } from 'iconsax-react';
import GithubIcon from 'assets/github';
import { Home2, InfoCircle, Sms, MessageQuestion, SearchNormal1, User, Menu as MenuIcon, ArrowDown2, Sun1 } from 'iconsax-react';

// link - custom style
const FooterLink = styled(Link)(({ theme }) => ({
  color: theme.palette.text.primary,
  '&:hover, &:active': {
    color: theme.palette.primary.main
  }
}));

// ==============================|| LANDING - FOOTER PAGE ||============================== //

export default function FooterBlock({ isFull }) {
  const linkSX = { color: 'text.secondary', fontWeight: 400, opacity: '0.6', cursor: 'pointer', '&:hover': { opacity: '1' } };

  return (
    <Box
      sx={{
        // bgcolor: 'rgba(255,255,255,0.85)',
        pb: 0,
        pt: 0,
        borderTop: '1px solid #b9b0e0',
        // position: 'static',
        left: 0,
        bottom: 0,
        // width: '100vw',
        boxShadow: '0 0 24px 0 rgba(160,139,230,0.08)',
        backdropFilter: 'blur(6px)'
        // display: 'flex'
        // justifyContent:"spa"
      }}
    >
      <Grid
        container
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          px: 4,
          py: 2,
          alignItems: 'flex-start',
          opacity: 5,
          backgroundColor: '#ffffff45'
        }}
      >
        <Grid
          container
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            px: 4,
            py: 2,
            alignItems: 'flex-start',
            width: '50%'
          }}
        >
          {/* Logo & Contact */}
          <Grid item xs={12} md={3}>
            <Stack direction="row" spacing={2} alignItems="flex-start" sx={{ mt: 2 }}>
              <Box>
                <img src={logo} alt="Naahlel Logo" style={{ width: 150, height: 'auto' }} />
                <Typography fontWeight={300} sx={{ fontSize: 20, color: '#222', mb: 1 }}>
                  +966-548 16 1616
                </Typography>
                <Stack direction="row" spacing={1}>
                  <Link
                    href="#"
                    underline="none"
                    width={30}
                    height={30}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    bgcolor={'#a08be6'}
                    borderRadius={50}
                  >
                    <Sms size={22} color="#fff" />
                  </Link>
                  <Link
                    href="#"
                    underline="none"
                    width={30}
                    height={30}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    bgcolor={'#a08be6'}
                    borderRadius={50}
                  >
                    <Instagram size={22} color="#fff" />
                  </Link>
                  <Link
                    href="#"
                    underline="none"
                    width={30}
                    height={30}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    bgcolor={'#a08be6'}
                    borderRadius={50}
                  >
                    <Global size={22} color="#ffff" />
                  </Link>
                  <Link
                    href="#"
                    underline="none"
                    width={30}
                    height={30}
                    display="flex"
                    alignItems="center"
                    justifyContent="center"
                    bgcolor={'#a08be6'}
                    borderRadius={50}
                  >
                    <Call size={22} color="#ffff" />
                  </Link>
                  <Link href="#" underline="none">
                    {/* <LinkedinSquare size={22} color="#a08be6" /> */}
                  </Link>
                </Stack>
              </Box>
            </Stack>
          </Grid>

          {/* About Naahlel */}
          <Grid item xs={12} md={2.5}>
            <Box sx={{ mt: 3, ml: 2 }}>
              <Typography sx={{ fontWeight: 300, fontSize: 16 }}>About Naahlel</Typography>
              <Stack spacing={0.5}>
                <Link href="#" underline="none" sx={{ color: '#222' }}>
                  <Typography variant="h7" fontWeight={600}>
                    About us
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Careers
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    News
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Investors
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Site Map
                  </Typography>
                </Link>
              </Stack>
            </Box>
          </Grid>

          {/* Naahlel Services */}
          <Grid item xs={12} md={2.5}>
            <Box sx={{ mt: 3, ml: 2 }}>
              <Typography sx={{ fontWeight: 300, fontSize: 16 }}>Naahlel Services</Typography>
              <Stack spacing={0.5}>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Courses
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Programs
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Learning Paths
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Classrooms
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Certifications
                  </Typography>
                </Link>
              </Stack>
            </Box>
          </Grid>

          {/* Naahlel Business */}
          <Grid item xs={12} md={2.5}>
            <Box sx={{ mt: 3, ml: 2 }}>
              <Typography sx={{ fontWeight: 300, fontSize: 16 }}>Naahlel Business</Typography>
              <Stack spacing={0.5}>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Visit Naahlel Business
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Teach on Naahlel
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Plans & Prices
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Affiliate Program
                  </Typography>
                </Link>
                <Link href="#" underline="none" sx={{ color: '#222', mt: '0 !important' }}>
                  <Typography variant="h7" fontWeight={600}>
                    Support
                  </Typography>
                </Link>
              </Stack>
            </Box>
          </Grid>
        </Grid>

        {/* Powered by */}
        <Grid item xs={12} md={1.5} sx={{ textAlign: 'center', mt: 3 }}>
          <Typography sx={{ fontWeight: 300, fontSize: 15, mb: 1 }}>Naahlel is powered by</Typography>
          <img src={logoFooter} alt="Powered by" style={{ width: 135, margin: '0 auto' }} />
        </Grid>
      </Grid>

      {/* Bottom Bar */}
      <Box
        sx={{
          // bgcolor: '#e0daf1',
          // borderTop: '1px solid #b9b0e0',

          px: 4,
          py: 1.5,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          backgroundColor: '#ffffff9e'
        }}
      >
        <Typography sx={{ fontWeight: 600, fontSize: 14 }}>Copy Rights Reserved © Naahlel 2025</Typography>
        <Stack direction="row" spacing={5}>
          <Link href="#" underline="none" sx={{ color: '#222', fontWeight: 600, fontSize: 14 }}>
            Terms and Conditions
          </Link>
          <Link href="#" underline="none" sx={{ color: '#222', fontWeight: 600, fontSize: 14 }}>
            Privacy & Cookies
          </Link>
          <Link href="#" underline="none" sx={{ color: '#222', fontWeight: 600, fontSize: 14 }}>
            Accessibility
          </Link>
        </Stack>
      </Box>
    </Box>
  );
}

FooterBlock.propTypes = { isFull: PropTypes.bool };
