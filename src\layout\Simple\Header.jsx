import React, { useEffect, useState } from 'react';
// import logo from './Naahel 360 Symbol-02.png';
import logo from 'assets/images/Naahel 360 Logo-05.png';

import { useMultiTenant } from '../../hooks/useMultiTenant';
import { useTenant } from '../../contexts/TenantContext';
import { API_BASE_URL, API_ENDPOINTS, MOODLE_CONFIG } from '../../config/config';
import {
  AppBar,
  Toolbar,
  Container,
  Box,
  Button,
  Stack,
  IconButton,
  Menu,
  MenuItem,
  Drawer,
  List,
  ListItemButton,
  ListItemText,
  useMediaQuery,
  useTheme,
  alpha,
  Avatar,
  Divider,
  Typography,
  Popper,
  ClickAwayListener,
  Paper
} from '@mui/material';
import {
  Home2,
  InfoCircle,
  Sms,
  MessageQuestion,
  SearchNormal1,
  User,
  Menu as MenuIcon,
  ArrowDown2,
  CloseCircle,
  Sun1,
  LanguageSquare
} from 'iconsax-react';
import { useNavigate, Link } from 'react-router';
// import icons from '../../data/icons';
// import icons from 'data/icons';
import IconSVG from '../../components/IconSVG';
import useConfig from '../../hooks/useConfig';

const NaqlelHeader = () => {
  const navigate = useNavigate();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [servicesAnchor, setServicesAnchor] = useState(null);
  const [mobileDrawer, setMobileDrawer] = useState(false);
  const [profileAnchor, setProfileAnchor] = useState(null);
  const [languageAnchor, setLanguageAnchor] = useState(null);
  const [apiStatus, setApiStatus] = useState('testing');
  const [headerInfo, setHeaderInfo] = useState(null);

  const { i18n, onChangeLocalization } = useConfig();

  // Get current tenant from context
  const { currentTenant } = useTenant();

  const {
    headerInfo: backendHeaderInfo,
    loading: tenantLoading,
    error: tenantError,
    isConnected,
    tenantName,
    tenantLogo,
    tenantColors,
    availableServices,
    hasAboutUs,
    hasServices,
    hasContactUs,
    hasFaq,
    refresh: refreshTenantData
  } = useMultiTenant(currentTenant?.id, i18n);

  // Use colors from current tenant (from API) or fallback to useMultiTenant
  const colors = {
    primary: currentTenant?.primaryColor || tenantColors?.primary || '#8c76dd',
    secondary: currentTenant?.secondaryColor || tenantColors?.secondary || '#FFE284',
    tertiary: currentTenant?.teritoryColor || tenantColors?.tertiary || '#FF8A19'
  };

  const handleServicesClick = (event) => {
    setServicesAnchor(event.currentTarget);
  };
  const handleServicesClose = () => {
    setServicesAnchor(null);
  };
  const handleProfileClick = (event) => {
    setProfileAnchor(event.currentTarget);
  };
  const handleProfileClose = () => {
    setProfileAnchor(null);
  };
  const toggleMobileDrawer = () => {
    setMobileDrawer(!mobileDrawer);
  };

  const handleLanguageClick = (event) => {
    setLanguageAnchor(event.currentTarget);
  };

  const handleLanguageClose = () => {
    setLanguageAnchor(null);
  };

  const handleLanguageChange = (lang) => {
    onChangeLocalization(lang);
    setLanguageAnchor(null);
  };

  const menuItems = [
    { label: 'Home', icon: <Home2 size={20} />, href: '/home', bold: true },
    ...(hasAboutUs ? [{ label: 'About us', icon: <InfoCircle size={20} />, href: '/about-us' }] : []),
    ...(availableServices?.length > 0 ? [{ label: 'Services', icon: null, href: '#', hasDropdown: true }] : []),
    ...(hasContactUs ? [{ label: 'Contact us', icon: <Sms size={20} />, href: '/contact-us' }] : []),
    ...(hasFaq ? [{ label: 'FAQs', icon: <MessageQuestion size={20} />, href: '/faq' }] : [])
  ];

  const servicesSubMenu = availableServices?.map((service) => service.name);

  const languages = [
    { code: 'en', name: 'English', flag: '/src/assets/images/lang-01.svg' },
    { code: 'ar', name: 'العربية', flag: '/src/assets/images/lang-02.svg' }
  ];

  const currentLanguage = languages.find((lang) => lang.code === i18n) || languages[0];

  const LogoSection = () => (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <img
        src={currentTenant?.logo || tenantLogo || headerInfo?.logo || logo}
        alt={currentTenant?.name || tenantName || headerInfo?.name || backendHeaderInfo?.name || 'Naahlel Logo'}
        style={{ width: 119, height: 'auto', borderRadius: 8 }}
      />
    </Box>
  );

  const DesktopMenu = () => (
    <Stack direction="row" spacing={0} sx={{ alignItems: 'center', ml: 4 }}>
      {menuItems.map((item, idx) => (
        <React.Fragment key={item.label}>
          <Button
            component={Link}
            to={item.href}
            endIcon={
              item.hasDropdown ? (
                <Box
                  sx={{
                    bgcolor: colors.primary,
                    borderRadius: '50%',
                    p: 0.5,
                    display: 'flex',
                    alignItems: 'center',
                    opacity: 0.2
                  }}
                >
                  <ArrowDown2 size={16} color="#fff" style={{ transform: servicesAnchor ? 'rotate(180deg)' : 'none' }} />
                </Box>
              ) : null
            }
            onClick={item.hasDropdown ? handleServicesClick : undefined}
            sx={{
              color: '#111',
              textTransform: 'none',
              background: 'none',
              boxShadow: 'none',
              '&:hover': {
                backgroundColor: 'transparent',
                color: colors.primary
              }
            }}
          >
            <Typography variant="h5" fontWeight={700}>
              {' '}
              {item.label}
            </Typography>
          </Button>
          {idx < menuItems.length - 1 && (
            <Divider
              orientation="vertical"
              flexItem
              sx={{
                mx: 2.5,
                borderColor: colors.primary,
                borderRightWidth: 0.2,
                opacity: 0.2,
                height: 32,
                alignSelf: 'center'
              }}
            />
          )}
        </React.Fragment>
      ))}
    </Stack>
  );

  const ActionButtons = () => (
    <Stack direction="row" spacing={2} sx={{ alignItems: 'center', ml: 2 }}>
      <Button
        variant="contained"
        startIcon={
          <Box
            sx={{
              borderRadius: '50%',

              display: 'flex',
              alignItems: 'center',
              opacity: 0.2
            }}
          >
            <ArrowDown2 size={1} style={{ transform: 'rotate(90deg)', fontSize: '1px !important' }} />
          </Box>
        }
        sx={{
          background: alpha(colors.primary, 0.2),
          textTransform: 'none',
          borderRadius: '25px',
          px: 2,
          // py: 1,
          color: '#404040',
          fontWeight: 700,
          fontSize: '15px',
          boxShadow: 'none',
          letterSpacing: 5,
          '&:hover': {
            background: alpha(colors.secondary, 0.2)
          }
        }}
      >
        SEARCH
      </Button>
      <Box>
        <IconButton
          onClick={handleLanguageClick}
          sx={{
            borderRadius: '50%',
            overflow: 'hidden',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: 'none',
            width: 40,
            height: 40,
            ml: 1
          }}
        >
          <img src={currentLanguage.flag} alt={currentLanguage.name} style={{ width: 30, height: 30 }} />
        </IconButton>
        <Menu
          anchorEl={languageAnchor}
          open={Boolean(languageAnchor)}
          onClose={handleLanguageClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right'
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right'
          }}
          PaperProps={{
            sx: {
              right: 200,
              width: 120,
              top: '88px !important',
              left: ' initial !important'
            }
          }}
        >
          {languages.map((lang) => (
            <MenuItem
              key={lang.code}
              selected={i18n === lang.code}
              onClick={() => handleLanguageChange(lang.code)}
              sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
            >
              <img src={lang.flag} alt={lang.name} style={{ width: 22, height: 22, borderRadius: '50%' }} />
              {lang.name}
            </MenuItem>
          ))}
        </Menu>
      </Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-around' }}>
        <IconButton
          sx={{
            color: colors.primary,
            // bgcolor: '#f3f0fa',
            '&:hover': {
              // bgcolor: '#e6e1f7'
            }
          }}
        >
          <img src="/src/assets/images/header-01.svg" alt="en" style={{ width: 21, height: 32 }} />
        </IconButton>
        <IconButton
          sx={{
            color: colors.primary,
            // bgcolor: '#f3f0fa',
            '&:hover': {
              // bgcolor: '#e6e1f7'
            }
          }}
        >
          <img src="/src/assets/images/header-02.svg" alt="en" style={{ width: 21, height: 32 }} />
        </IconButton>
        <IconButton
          sx={{
            color: colors.primary,
            // bgcolor: '#f3f0fa',
            '&:hover': {
              // bgcolor: '#e6e1f7'
            }
          }}
        >
          <img src="/src/assets/images/header-03.svg" alt="en" style={{ width: 21, height: 32 }} />
        </IconButton>
      </Box>

      <Button
        variant="outlined"
        component={Link}
        to={'/login'}
        sx={{
          textTransform: 'none',
          borderColor: '#404040',
          color: '#404040',
          borderRadius: '20px',
          // background: '#f8f9ff',
          py: 0.1,
          letterSpacing: 1,
          px: 3,
          fontSize: '16px',
          fontWeight: 400,
          boxShadow: 'none',
          '&:hover': {
            backgroundColor: '',
            borderColor: '#404040',
            color: '#404040'
          }
        }}
      >
        LOGIN
      </Button>
      <IconButton
        sx={{
          // color: colors.primary,

          // border: `1px solid ${colors.primary}`,
          borderRadius: '50%',
          p: 0,
          '&:hover': {}
        }}
      >
        <img src="/src/assets/images/header-04.svg" alt="en" style={{ width: '100%', height: 32 }} />
      </IconButton>
      <IconButton
        sx={{
          color: colors.primary,
          bgcolor: '#fff',
          p: 0.8,

          borderRadius: '50%',
          '&:hover': {
            bgcolor: '#e6e1f7',
            color: colors.secondary
          }
        }}
      >
        <img src="/src/assets/images/header-05.svg" alt="en" style={{ width: '100%', height: 32 }} />
      </IconButton>
    </Stack>
  );

  const MobileMenu = () => (
    <Drawer
      anchor="right"
      open={mobileDrawer}
      onClose={toggleMobileDrawer}
      sx={{
        '& .MuiDrawer-paper': {
          width: 280,
          background: 'linear-gradient(180deg, #f8f9ff 0%, #ffffff 100%)'
        }
      }}
    >
      <Box sx={{ p: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <LogoSection />
          <IconButton onClick={toggleMobileDrawer}>
            <CloseCircle size={22} color={colors.primary} />
          </IconButton>
        </Box>

        <List>
          {menuItems.map((item) => (
            <ListItemButton
              key={item.label}
              component={Link}
              to={item.href}
              sx={{
                borderRadius: 2,
                mb: 1,
                '&:hover': {
                  backgroundColor: `${colors.primary}20`
                }
              }}
            >
              {item.icon && React.cloneElement(item.icon, { color: colors.primary })}
              <ListItemText
                primary={item.label}
                sx={{
                  ml: 2,
                  color: colors.primary
                }}
              />
            </ListItemButton>
          ))}
        </List>

        <Box sx={{ mt: 3, space: 2 }}>
          <Button
            fullWidth
            variant="contained"
            sx={{
              background: colors.primary,
              textTransform: 'none',
              borderRadius: '25px',
              py: 1.5,
              mb: 2,
              '&:hover': {
                background: colors.secondary
              }
            }}
          >
            REQUEST A DEMO
          </Button>

          <Button
            fullWidth
            variant="outlined"
            sx={{
              textTransform: 'none',
              borderRadius: '20px',
              py: 1.5,
              borderColor: colors.primary,
              color: colors.primary,
              '&:hover': {
                borderColor: colors.secondary,
                color: colors.secondary
              }
            }}
          >
            LOGIN
          </Button>
        </Box>
      </Box>
    </Drawer>
  );

  return (
    <>
      <AppBar
        position="sticky"
        elevation={0}
        sx={{
          background: colors.secondary,
          backdropFilter: 'blur(10px)',
          borderBottom: `1px solid ${colors.primary}20`,
          borderRadius: '40px',

          maxWidth: '98%',
          mx: 'auto',
          boxShadow: 'none'
        }}
      >
        <Toolbar sx={{ py: 1, minHeight: '70px !important' }}>
          <LogoSection />
          {!isMobile && <DesktopMenu />}
          <Box sx={{ flexGrow: 1 }} />
          {!isMobile ? (
            <Box sx={{ ml: 4 }}>
              <ActionButtons />
            </Box>
          ) : (
            <IconButton
              onClick={toggleMobileDrawer}
              sx={{
                color: colors.primary,
                ml: 1,
                '&:hover': {
                  color: colors.secondary
                }
              }}
            >
              <MenuIcon size={22} />
            </IconButton>
          )}
        </Toolbar>
      </AppBar>
      <Menu
        anchorEl={servicesAnchor}
        open={Boolean(servicesAnchor)}
        onClose={handleServicesClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
      >
        {servicesSubMenu.map((item) => (
          <MenuItem
            key={item}
            onClick={handleServicesClose}
            sx={{
              '&:hover': {
                color: colors.primary
              }
            }}
          >
            {item}
          </MenuItem>
        ))}
      </Menu>
      <MobileMenu />
    </>
  );
};

export default NaqlelHeader;
