// assets
import {
  I24Support,
  MessageProgramming,
  Home3,
  Book,
  BookSaved,
  TrendUp,
  CpuCharge,
  Calculator,
  Notification,
  DocumentText,
  Bill,
  People,
  Message,
  Monitor
} from 'iconsax-react';

// icons
const icons = {
  maintenance: MessageProgramming,
  contactus: I24Support,
  dashboard: Home3,
  trainings: Book,
  courses: BookSaved,
  paths: TrendUp,
  programs: CpuCharge,
  exams: Calculator,
  requests: Notification,
  certificates: DocumentText,
  payments: Bill,
  myTeam: People,
  forum: Message,
  surveys: Monitor,
  userGuide: DocumentText,
  supportTicket: I24Support
};

// ==============================|| MENU ITEMS - PAGES ||============================== //

const community = {
  id: 'group-community',
  title: 'community',
  type: 'group',
  children: [
    // Community section as a collapse instead of group

    {
      id: 'myTeam',
      title: 'my-team',
      type: 'item',
      url: '#',
      icon: icons.myTeam
    },
    {
      id: 'forum',
      title: 'forum',
      type: 'item',
      url: '#',
      icon: icons.forum
    },
    {
      id: 'surveys',
      title: 'surveys',
      type: 'item',
      url: '#',
      icon: icons.surveys
    }
  ]
};

export default community;
