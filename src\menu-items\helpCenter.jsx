// assets
import {
  I24Support,
  MessageProgramming,
  Home3,
  Book,
  BookSaved,
  TrendUp,
  CpuCharge,
  Calculator,
  Notification,
  DocumentText,
  Bill,
  People,
  Message,
  Monitor
} from 'iconsax-react';

// icons
const icons = {
  maintenance: MessageProgramming,
  contactus: I24Support,
  dashboard: Home3,
  trainings: Book,
  courses: BookSaved,
  paths: TrendUp,
  programs: CpuCharge,
  exams: Calculator,
  requests: Notification,
  certificates: DocumentText,
  payments: Bill,
  myTeam: People,
  forum: Message,
  surveys: Monitor,
  userGuide: DocumentText,
  supportTicket: I24Support
};

// ==============================|| MENU ITEMS - PAGES ||============================== //

const helpCenter = {
  id: 'group-help-center',
  title: 'help-center',
  type: 'group',
  children: [
    // Help Center section as a collapse instead of group

    {
      id: 'userGuide',
      title: 'user-guide',
      type: 'item',
      url: '#',
      icon: icons.userGuide
    },
    {
      id: 'supportTicket',
      title: 'support-ticket',
      type: 'item',
      url: '#',
      icon: icons.supportTicket
    }
  ]
};

export default helpCenter;
