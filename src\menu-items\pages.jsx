// assets
import {
  I24Support,
  MessageProgramming,
  Home3,
  Book,
  BookSaved,
  TrendUp,
  CpuCharge,
  Calculator,
  Notification,
  DocumentText,
  Bill,
  People,
  Message,
  Monitor
} from 'iconsax-react';
import {
  APP_DASHBOARD_PATH,
  APP_TRAININGS_HOME_PATH,
  APP_TRAININGS_PATHS_PATH,
  APP_EXAMS_PATH,
  APP_REQUESTS_PATH,
  APP_CERTIFICATES_PATH,
  APP_PAYMENTS_PATH
} from '../datatypes/routes';

// icons
const icons = {
  maintenance: MessageProgramming,
  contactus: I24Support,
  dashboard: Home3,
  trainings: Book,
  courses: BookSaved,
  paths: TrendUp,
  programs: CpuCharge,
  exams: Calculator,
  requests: Notification,
  certificates: DocumentText,
  payments: Bill,
  myTeam: People,
  forum: Message,
  surveys: Monitor,
  userGuide: DocumentText,
  supportTicket: I24Support
};

// ==============================|| MENU ITEMS - PAGES ||============================== //

const pages = {
  id: 'group-pages',
  title: 'pages',
  type: 'group',
  children: [
    {
      id: 'dashboard',
      title: 'dashboard',
      type: 'item',
      url: APP_DASHBOARD_PATH,
      icon: icons.dashboard
    },
    {
      id: 'trainings',
      title: 'trainings',
      type: 'collapse',
      icon: icons.trainings,
      children: [
        {
          id: 'myCourses',
          title: 'courses',
          type: 'item',
          url: APP_DASHBOARD_PATH + APP_TRAININGS_HOME_PATH + APP_TRAININGS_PATHS_PATH.myCourses,
          icon: icons.courses
        },
        {
          id: 'classrooms',
          title: 'classrooms',
          type: 'item',
          url: APP_DASHBOARD_PATH + APP_TRAININGS_HOME_PATH + APP_TRAININGS_PATHS_PATH.classrooms,
          icon: icons.trainings
        },
        {
          id: 'paths',
          title: 'paths',
          type: 'item',
          url: APP_DASHBOARD_PATH + APP_TRAININGS_HOME_PATH + APP_TRAININGS_PATHS_PATH.paths,
          icon: icons.paths
        },
        {
          id: 'programs',
          title: 'programs',
          type: 'item',
          url: APP_DASHBOARD_PATH + APP_TRAININGS_HOME_PATH + APP_TRAININGS_PATHS_PATH.programs,
          icon: icons.programs
        }
      ]
    },
    {
      id: 'exams',
      title: 'exams',
      type: 'item',
      url: APP_EXAMS_PATH,
      icon: icons.exams
    },
    {
      id: 'requests',
      title: 'requests',
      type: 'item',
      url: APP_REQUESTS_PATH,
      icon: icons.requests
    },
    {
      id: 'certificates',
      title: 'certificates',
      type: 'item',
      url: APP_CERTIFICATES_PATH,
      icon: icons.certificates
    },
    {
      id: 'payments',
      title: 'payments',
      type: 'item',
      url: APP_PAYMENTS_PATH,
      icon: icons.payments
    }
  ]
};

export default pages;
