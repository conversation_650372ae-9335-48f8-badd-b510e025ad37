import React from 'react';
import TenantHeader from '../components/TenantHeader';
import { useTenant } from '../contexts/TenantContext';

/**
 * Test page for Multi-Tenant Header with API integration
 */
const HeaderTestPage = () => {
  const { currentTenant } = useTenant();

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
      {/* Multi-Tenant Header */}
      <TenantHeader />

      {/* Page Content */}
      <main style={{ padding: '40px 20px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          <h1>Multi-Tenant Header Test Page</h1>
          <div
            style={{
              backgroundColor: 'white',
              padding: '30px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginBottom: '30px'
            }}
          >
            <h2>🏢 Current Tenant Information</h2>

            {currentTenant ? (
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
                <div>
                  <strong>Tenant Name:</strong>
                  <p>{currentTenant.name}</p>
                </div>
                <div>
                  <strong>Tenant ID:</strong>
                  <p>{currentTenant.id}</p>
                </div>
                <div>
                  <strong>Subdomain:</strong>
                  <p>{currentTenant.subdomain}</p>
                </div>
                <div>
                  <strong>Language:</strong>
                  <p>{currentTenant.language}</p>
                </div>
                <div>
                  <strong>Direction:</strong>
                  <p>{currentTenant.direction}</p>
                </div>
                <div>
                  <strong>API Endpoint:</strong>
                  <p style={{ fontSize: '12px', wordBreak: 'break-all' }}>{currentTenant.apiEndpoint}</p>
                </div>
              </div>
            ) : (
              <p>❌ No tenant configured</p>
            )}
          </div>
          {/* API Integration Info */}
          <div
            style={{
              backgroundColor: 'white',
              padding: '30px',
              borderRadius: '8px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              marginBottom: '30px'
            }}
          >
            <h2>📡 API Integration Status</h2>

            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '20px' }}>
              <div>
                <strong>API Endpoint:</strong>
                <p style={{ fontSize: '12px', wordBreak: 'break-all' }}>{currentTenant?.apiEndpoint || 'Not configured'}</p>
              </div>
              <div>
                <strong>Web Service Function:</strong>
                <p style={{ fontSize: '12px' }}>{currentTenant?.moodleConfig?.functions?.getHeaderInfo || 'Not configured'}</p>
              </div>
              <div>
                <strong>Tenant ID for API:</strong>
                <p>{currentTenant?.id || 'Not set'}</p>
              </div>
              <div>
                <strong>Language for API:</strong>
                <p>{currentTenant?.language || 'Not set'}</p>
              </div>
            </div>

            <div
              style={{
                marginTop: '20px',
                padding: '15px',
                backgroundColor: '#e3f2fd',
                borderRadius: '4px',
                border: '1px solid #2196F3'
              }}
            >
              <h3>🔄 How the API Works:</h3>
              <ol>
                <li>Header component detects current tenant from subdomain</li>
                <li>
                  Calls <code>getTenantHeaderInfo(tenantId, language)</code>
                </li>
                <li>
                  Makes POST request to: <code>{currentTenant?.apiEndpoint}/webservice/rest/server.php</code>
                </li>
                <li>
                  Uses function: <code>local_guestapi_get_tenant_header_info</code>
                </li>
                <li>Displays returned data (logo, name, colors, etc.)</li>
              </ol>
            </div>
          </div>
          {/* Instructions */}
          <div
            style={{
              backgroundColor: '#e3f2fd',
              padding: '30px',
              borderRadius: '8px',
              border: '1px solid #2196F3'
            }}
          >
            <h2>🚀 How to Test Multi-Tenant</h2>

            <div style={{ marginBottom: '20px' }}>
              <h3>Method 1: Using Subdomains (Production)</h3>
              <ul>
                <li>
                  <strong>tenant1.yourdomain.com</strong> - Arabic tenant (ID: 83)
                </li>
                <li>
                  <strong>tenant2.yourdomain.com</strong> - English tenant (ID: 84)
                </li>
                <li>
                  <strong>yourdomain.com</strong> - Default tenant
                </li>
              </ul>
            </div>

            <div style={{ marginBottom: '20px' }}>
              <h3>Method 2: Local Development</h3>
              <p>
                For local testing, you can modify your <code>/etc/hosts</code> file:
              </p>
              <pre
                style={{
                  backgroundColor: '#f5f5f5',
                  padding: '10px',
                  borderRadius: '4px',
                  fontSize: '12px',
                  overflow: 'auto'
                }}
              >
                {`127.0.0.1 tenant1.localhost
127.0.0.1 tenant2.localhost
127.0.0.1 localhost`}
              </pre>
              <p>Then access:</p>
              <ul>
                <li>
                  <strong>http://tenant1.localhost:3000</strong>
                </li>
                <li>
                  <strong>http://tenant2.localhost:3000</strong>
                </li>
                <li>
                  <strong>http://localhost:3000</strong>
                </li>
              </ul>
            </div>

            <div>
              <h3>Current Detection</h3>
              <p>
                <strong>Hostname:</strong> {window.location.hostname}
              </p>
              <p>
                <strong>Detected Subdomain:</strong> {currentTenant?.subdomain || 'default'}
              </p>
              <p>
                <strong>Full URL:</strong> {window.location.href}
              </p>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default HeaderTestPage;
