import React from 'react';
import { FormattedMessage } from 'react-intl';

// material-ui
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import Stack from '@mui/material/Stack';
import LinearProgress from '@mui/material/LinearProgress';
import Button from '@mui/material/Button';
import Card from '@mui/material/Card';
import CardContent from '@mui/material/CardContent';
import Chip from '@mui/material/Chip';
import Avatar from '@mui/material/Avatar';

// iconsax-react icons
import { PlayCircle, Calendar1, Award, Book1, Clock, ArrowDown2 } from 'iconsax-react';

// ==============================|| DASHBOARD ||============================== //

export default function Dashboard() {
  // Dummy data for Learning Journey
  const continueLearningCourses = [
    { id: 1, title: 'Title', progress: 80 },
    { id: 2, title: 'Title', progress: 60 },
    { id: 3, title: 'Title', progress: 50 }
  ];
  const overallProgress = 71;
  const completedCoursesCount = 6;
  const totalCoursesCount = 10;

  // Dummy data for Badges
  const unlockedBadges = [
    { id: 1, name: 'Badge' },
    { id: 2, name: 'Badge' },
    { id: 3, name: 'Badge' },
    { id: 4, name: 'Badge' },
    { id: 5, name: 'Badge' }
  ];
  const nextBadgeProgress = 10;
  const nextBadgeTotal = 12;

  // Dummy data for Certificates
  const certificates = [
    { id: 1, title: 'Title', date: '29 April 2026' },
    { id: 2, title: 'Title', date: '29 April 2026' },
    { id: 3, title: 'Title', date: '29 April 2026' }
  ];

  // Dummy data for Calendar
  const calendarEvents = [
    { id: 1, title: 'Event Title', time: '9:45 - 10:30', timeLabel: '10:00' },
    { id: 2, title: 'Event Title', time: '11:00 - 11:45', timeLabel: '11:00' },
    { id: 3, title: 'Event Title', time: '12:00 - 12:45', timeLabel: '11:30' }
  ];

  return (
    <Box sx={{ width: '100%', minHeight: '100vh' }}>
      <Grid container spacing={3} sx={{ width: '100%' }}>
        {/* Welcome Section */}
        <Grid size={12} sx={{ background: '#f9fafb', p: 2, borderRadius: 2 }}>
          <Box sx={{ background: '#f9fafb' }}>
            <Typography variant="h4" color="text.secondary" sx={{ fontWeight: 'bold' }}>
              <FormattedMessage id="dashboard" />
            </Typography>
            <Typography variant="body1" color="text.secondary">
              <FormattedMessage id="welcome.message" defaultMessage="Naheel wishes you a good and productive day." />
            </Typography>
          </Box>
        </Grid>

        {/* Top Row: Learning Journey (left) and Calendar (right) */}
        <Grid container spacing={3} size={12}>
          {/* Your Learning Journey Section */}
          {/* Your Learning Journey Section */}
          <Grid size={{ xs: 12, lg: 9 }}>
            <Card
              sx={{
                height: 'fit-content',
                background: '#f9fafb',
                borderRadius: 3,
                boxShadow: 'none'
              }}
            >
              <CardContent sx={{ p: 3 }}>
                {/* Header */}
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Your Learning Journey
                  </Typography>
                  <Button variant="text" size="small" endIcon={<ArrowDown2 size={16} />} sx={{ color: '#6366f1', textTransform: 'none' }}>
                    Courses
                  </Button>
                </Stack>

                {/* Continue Learning Section */}
                <Box sx={{ mb: 4 }}>
                  <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 2 }}>
                    <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                      Continue Learning
                    </Typography>
                    <Button variant="text" size="small" sx={{ color: '#6366f1', textTransform: 'none' }}>
                      See All
                    </Button>
                  </Stack>

                  <Stack spacing={2}>
                    {continueLearningCourses.map((course) => (
                      <Box
                        key={course.id}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          gap: 2
                        }}
                      >
                        <Stack>
                          <Typography variant="body2" sx={{ fontWeight: 500, marginBottom: 0 }}>
                            {course.title}
                          </Typography>
                        </Stack>

                        {/* Progress Bar with Dynamic Percentage Position */}
                        <Box position="relative" width="100%">
                          <LinearProgress
                            variant="determinate"
                            value={course.progress}
                            sx={{
                              height: 20,
                              borderRadius: 5,
                              backgroundColor: '#e5e7eb',
                              '& .MuiLinearProgress-bar': {
                                backgroundColor: course.progress >= 70 ? '#92e3a9' : '#dee391',
                                borderRadius: 5
                              }
                            }}
                          />
                          <Typography
                            variant="caption"
                            sx={{
                              position: 'absolute',
                              top: '50%',
                              left: `calc(${course.progress}% / 2)`,
                              transform: 'translate(-50%, -50%)',
                              color: '#fff',
                              fontWeight: 600,
                              fontSize: '0.75rem',
                              whiteSpace: 'nowrap'
                            }}
                          >
                            {course.progress}%
                          </Typography>
                        </Box>

                        {/* Play Button */}
                        <Box
                          sx={{
                            width: 28,
                            height: 28,
                            borderRadius: '50%',
                            backgroundColor: '#f3f4f6',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            cursor: 'pointer',
                            '&:hover': {
                              backgroundColor: '#e5e7eb'
                            }
                          }}
                        >
                          <PlayCircle size={16} color="#4b5563" />
                        </Box>
                      </Box>
                    ))}
                  </Stack>
                </Box>

                {/* Your Progress Section */}
                <Box>
                  <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                    Your Progress
                  </Typography>
                  <Box>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', justifyContent: 'end', margin: 1 }}>
                      {completedCoursesCount} of {totalCoursesCount} Courses Completed
                    </Typography>
                    <Box position="relative" width="100%">
                      <LinearProgress
                        variant="determinate"
                        value={overallProgress}
                        sx={{
                          height: 20,
                          borderRadius: 5,
                          backgroundColor: '#e5e7eb',
                          '& .MuiLinearProgress-bar': {
                            backgroundColor: overallProgress >= 70 ? '#92e3a9' : '#dee391',
                            borderRadius: 5
                          }
                        }}
                      />
                      <Typography
                        variant="caption"
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: `calc(${overallProgress}% / 2)`,
                          transform: 'translate(-50%, -50%)',
                          color: '#fff',
                          fontWeight: 600,
                          fontSize: '0.75rem',
                          whiteSpace: 'nowrap'
                        }}
                      >
                        {overallProgress}%
                      </Typography>
                    </Box>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Right Column for Calendar */}
          <Grid size={{ xs: 12, lg: 3 }}>
            <Card sx={{ height: 'fit-content', background: '#f9fafb', borderRadius: 2, boxShadow: 'none' }}>
              <CardContent sx={{ p: 3 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Calendar
                    <FormattedMessage id="sample-page" />
                  </Typography>
                  <Button variant="text" size="small" endIcon={<ArrowDown2 size={16} />} sx={{ color: '#6366f1', textTransform: 'none' }}>
                    Today
                  </Button>
                </Stack>

                <Stack spacing={2}>
                  {calendarEvents.map((event) => (
                    <Box key={event.id}>
                      <Stack direction="row" spacing={2}>
                        <Typography
                          variant="caption"
                          sx={{
                            display: 'flex',
                            justifyContent: 'center',
                            color: 'text.secondary',
                            minWidth: '40px',
                            fontWeight: 500,
                            mt: 0.5
                          }}
                        >
                          {event.timeLabel}
                        </Typography>
                        <Box
                          sx={{
                            flexGrow: 1,
                            backgroundColor: '#fff',
                            borderRadius: 2,
                            p: 2,
                            borderLeft: '3px solid #6366f1'
                          }}
                        >
                          <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                            {event.title}
                          </Typography>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Clock size={12} color="#6b7280" />
                            <Typography variant="caption" color="text.secondary">
                              {event.time}
                            </Typography>
                          </Stack>
                        </Box>
                      </Stack>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        {/* Badges and Certificates Sections (Bottom Row) */}
        <Grid container spacing={3} size={9}>
          {/* Badges Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card sx={{ height: 'fit-content', background: '#f9fafb', borderRadius: 2, boxShadow: 'none' }}>
              <CardContent sx={{ p: 3 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Badges
                  </Typography>
                  <Button variant="text" size="small" sx={{ color: '#6366f1', textTransform: 'none' }}>
                    See All
                  </Button>
                </Stack>

                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                  Unlocked
                </Typography>
                <Stack direction="row" spacing={2} sx={{ mb: 4, flexWrap: 'wrap' }}>
                  {unlockedBadges.map((badge) => (
                    <Box key={badge.id} sx={{ textAlign: 'center' }}>
                      <Avatar
                        sx={{
                          width: 56,
                          height: 56,
                          backgroundColor: '#fbbf24',
                          mb: 1,
                          mx: 'auto'
                        }}
                      >
                        <Award size={24} color="white" variant="Bold" />
                      </Avatar>
                      <Typography variant="caption" color="text.secondary">
                        Badge
                      </Typography>
                    </Box>
                  ))}
                </Stack>

                <Typography variant="subtitle1" sx={{ fontWeight: 600, mb: 2 }}>
                  Next
                </Typography>
                <Stack direction="row" alignItems="center" spacing={2}>
                  <Avatar
                    sx={{
                      width: 48,
                      height: 48,
                      backgroundColor: '#e5e7eb'
                    }}
                  >
                    <Award size={20} color="#9ca3af" />
                  </Avatar>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                      Complete {nextBadgeTotal - nextBadgeProgress} more courses to unlock
                    </Typography>
                    <LinearProgress
                      variant="determinate"
                      value={(nextBadgeProgress / nextBadgeTotal) * 100}
                      sx={{
                        height: 6,
                        borderRadius: 3,
                        backgroundColor: '#f3f4f6',
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: '#3b82f6',
                          borderRadius: 3
                        }
                      }}
                    />
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, display: 'block' }}>
                      {nextBadgeProgress}/{nextBadgeTotal}
                    </Typography>
                  </Box>
                </Stack>
              </CardContent>
            </Card>
          </Grid>

          {/* Certificates Section */}
          <Grid size={{ xs: 12, md: 6 }}>
            <Card sx={{ height: 'fit-content', background: '#f9fafb', borderRadius: 2, boxShadow: 'none' }}>
              <CardContent sx={{ p: 3 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                  <Typography variant="h5" sx={{ fontWeight: 600 }}>
                    Certificates
                  </Typography>
                  <Button variant="text" size="small" sx={{ color: '#6366f1', textTransform: 'none' }}>
                    See All
                  </Button>
                </Stack>

                <Stack spacing={2}>
                  {certificates.map((certificate) => (
                    <Box
                      key={certificate.id}
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 2,
                        p: 2,
                        backgroundColor: '#f9fafb',
                        borderRadius: 2,
                        border: '1px solid #f3f4f6'
                      }}
                    >
                      <Avatar
                        sx={{
                          width: 40,
                          height: 40,
                          backgroundColor: '#dbeafe',
                          borderRadius: 2
                        }}
                      >
                        <Book1 size={20} color="#3b82f6" variant="Bold" />
                      </Avatar>
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2" sx={{ fontWeight: 600, mb: 0.5 }}>
                          {certificate.title}
                        </Typography>
                        <Stack direction="row" alignItems="center" spacing={1}>
                          <Calendar1 size={12} color="#6b7280" />
                          <Typography variant="caption" color="text.secondary">
                            {certificate.date}
                          </Typography>
                        </Stack>
                      </Box>
                      <Button variant="text" size="small" sx={{ minWidth: 'auto', p: 1 }}>
                        •••
                      </Button>
                    </Box>
                  ))}
                </Stack>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
}
