import React, { useEffect, useState } from 'react';

// material-ui
import Grid from '@mui/material/Grid2';
import Stack from '@mui/material/Stack';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';
import ToggleButton from '@mui/material/ToggleButton';
import ToggleButtonGroup from '@mui/material/ToggleButtonGroup';

// project-imports
import SearchBar from 'components/shared/SearchBar';
import FilterBar from 'components/shared/FilterBar';
import ActiveFilters from 'components/shared/ActiveFilters';
import CourseCard from 'components/shared/CourseCard';
import CourseCardPlaceholder from 'components/shared/CourseCardPlaceholder';
import CoursesTable from 'components/shared/CoursesTable';

// assets
import { Grid1, RowVertical, ArrowDown2, Share, Eye, CloseCircle } from 'iconsax-react';
import { getMyCourses } from '../../../api/myCoursesApi';

// ==============================|| MY COURSES ||============================== //

export default function MyCourses() {
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState('grid');
  const [myCoursesList, setMyCoursesList] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [activeFilters, setActiveFilters] = useState([
    { key: 'enrollmentDate', label: 'Enrollment Date', value: '30 April 2025' },
    { key: 'category', label: 'Category', value: 'Category' }
  ]);

  const clearFilter = (filterKey) => {
    setActiveFilters(activeFilters.filter((filter) => filter.key !== filterKey));
  };

  const clearAllFilters = () => {
    setActiveFilters([]);
  };

  const handleFilterClick = (filterType) => {
    // Handle filter click logic here
    console.log('Filter clicked:', filterType);
  };

  const handleViewModeChange = (event, newViewMode) => {
    if (newViewMode !== null) {
      setViewMode(newViewMode);
    }
  };

  const fetchMyCourses = async () => {
    setLoading(true);

    getMyCourses()
      .then((response) => {
        // console.log(response);

        if (!response.success) {
          if (response.message === 'Unauthorized. Please log in again.') {
            setError('Unauthorized. Please log in again.');
          } else {
            setError(response.message || 'Failed to fetch courses.');
          }
        } else {
          setMyCoursesList(response.data.data.mycourses || []);
        }
      })
      .catch((err) => {
        console.error('Error fetching courses:', err);
        setError('An error occurred while fetching courses.');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    fetchMyCourses();
  }, []);

  useEffect(() => {
    const load = async () => {
      const response = await fetch('API');
      const data = await response.json();
    };
    load();
  }, []);

  useEffect(() => {
    const load = async () => {
      try {
        const response = await fetch('API');
        const data = await response.json();
      } catch (err) {
        console.error('Error:', err.message);
      }
    };

    load();
  }, []);

  const filteredCourses = myCoursesList.filter(
    (course) =>
      course.fullname.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (course.lang && course.lang.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <Box sx={{ width: '100%', minHeight: '100vh', p: 0, m: 0 }}>
      <Grid container sx={{ maxWidth: 'none', width: '100%' }}>
        {/* Search Bar */}
        <Grid size={12}>
          <Box sx={{ maxWidth: 400, mb: 3 }}>
            <SearchBar value={searchTerm} onChange={(e) => setSearchTerm(e.target.value)} />
          </Box>
        </Grid>

        {/* Filters and View Controls */}
        <Grid size={12}>
          <Stack direction="row" sx={{ justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
            {/* Filters */}
            <FilterBar onFilterClick={handleFilterClick} />

            {/* View Toggle */}
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={handleViewModeChange}
              size="small"
              sx={{ backgroundColor: 'white', border: '1px solid #e0e0e0' }}
            >
              <ToggleButton value="grid" sx={{ px: 2 }}>
                <Grid1 size={16} />
              </ToggleButton>
              <ToggleButton value="list" sx={{ px: 2 }}>
                <RowVertical size={16} />
              </ToggleButton>
            </ToggleButtonGroup>
          </Stack>
        </Grid>

        {/* Active Filters */}
        <Grid size={12}>
          <ActiveFilters
            filters={activeFilters}
            onClearFilter={clearFilter}
            onClearAll={clearAllFilters}
            totalItems={filteredCourses.length}
          />
        </Grid>

        {/* Content - Grid or Table based on viewMode */}
        <Grid size={12}>
          {viewMode === 'list' ? (
            <CoursesTable loading={loading} error={error} filteredCourses={filteredCourses} />
          ) : (
            <Grid container spacing={3}>
              {loading ? (
                Array.from(new Array(4)).map((_, index) => (
                  <Grid key={index} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                    <CourseCardPlaceholder />
                  </Grid>
                ))
              ) : error ? (
                <Grid size={12} sx={{ textAlign: 'center', color: 'error.main' }}>
                  <Typography variant="h6">{error}</Typography>
                </Grid>
              ) : filteredCourses.length === 0 ? (
                <Grid size={12} sx={{ textAlign: 'center' }}>
                  <Typography variant="h6">No courses found.</Typography>
                </Grid>
              ) : (
                filteredCourses.map((course) => (
                  <Grid key={course.id} size={{ xs: 12, sm: 6, md: 4, lg: 3 }}>
                    <CourseCard course={course} />
                  </Grid>
                ))
              )}
            </Grid>
          )}
        </Grid>
      </Grid>
    </Box>
  );
}
