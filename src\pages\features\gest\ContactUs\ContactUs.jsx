import React from 'react';
import { Formik, Form, Field, ErrorMessage } from 'formik';
import * as Yup from 'yup';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  Grid,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  Stack
} from '@mui/material';

// Validation schema using Yup
const validationSchema = Yup.object({
  name: Yup.string().min(2, 'Name must be at least 2 characters').required('Name is required'),
  email: Yup.string().email('Invalid email address').required('Email is required'),
  organizationName: Yup.string(),
  phone: Yup.string().matches(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number'),
  howDidYouHear: Yup.string(),
  subject: Yup.string(),
  message: Yup.string().min(10, 'Message must be at least 10 characters')
});

function ContactUs() {
  const initialValues = {
    name: '',
    email: '',
    organizationName: '',
    phone: '',
    howDidYouHear: '',
    subject: '',
    message: ''
  };

  const handleSubmit = (values, { setSubmitting, resetForm }) => {
    // Simulate API call
    setTimeout(() => {
      console.log('Form submitted:', values);
      alert('Thank you for your message! We will get back to you soon.');
      resetForm();
      setSubmitting(false);
    }, 1000);
  };

  return (
    <Box
      sx={{
        // minHeight: '100vh',
        py: 6,
        px: 4
      }}
    >
      <Box sx={{ mx: 'auto' }}>
        <Card
          sx={{
            borderRadius: 3,
            boxShadow: 'none',
            // border: '1px solid rgba(255, 255, 255, 0.2)',
            // backdropFilter: 'blur(10px)',
            background: 'transparent',
            border: 'none'
            // overflow: 'visible'
          }}
        >
          <CardContent sx={{ p: 6 }}>
            {/* Header */}
            <Box sx={{ textAlign: 'start', mb: 4 }}>
              <Typography
                variant="overline"
                sx={{
                  fontWeight: 300,
                  letterSpacing: 2,
                  color: '#64748b',
                  display: 'block',
                  mb: 1
                }}
              >
                CONTACT US
              </Typography>
              <Typography
                variant="h3"
                sx={{
                  fontWeight: 900,
                  color: '#1e293b',
                  fontSize: { xs: '1.8rem', md: '2.2rem' },
                  mb: 1
                }}
              >
                Let's Communicate
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  color: '#475569',
                  fontWeight: 400,
                  letterSpacing: 0.5
                }}
              >
                We Value Your Thoughts
              </Typography>
            </Box>

            {/* Form */}
            <Formik initialValues={initialValues} validationSchema={validationSchema} onSubmit={handleSubmit}>
              {({ values, errors, touched, handleChange, handleBlur, isSubmitting }) => (
                <Form>
                  <Grid container spacing={1}>
                    {/* Name and Organization Name */}
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        name="name"
                        placeholder="Name*"
                        value={values.name}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.name && Boolean(errors.name)}
                        helperText={touched.name && errors.name}
                        required
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#FFFFFF',
                            borderRadius: 2,
                            '& input::placeholder': {
                              color: '#404040',
                              opacity: 1,
                              fontWeight: 500
                            },
                            '&:hover': {
                              backgroundColor: '#ffffff'
                            },
                            '&.Mui-focused': {
                              backgroundColor: '#ffffff'
                            }
                          }
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        name="organizationName"
                        placeholder="Organization Name"
                        value={values.organizationName}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.organizationName && Boolean(errors.organizationName)}
                        helperText={touched.organizationName && errors.organizationName}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#FFFFFF',
                            borderRadius: 2,
                            '& input::placeholder': {
                              color: '#404040',
                              opacity: 1,
                              fontWeight: 500
                            },
                            '&:hover': {
                              backgroundColor: '#ffffff'
                            },
                            '&.Mui-focused': {
                              backgroundColor: '#ffffff'
                            }
                          }
                        }}
                      />
                    </Grid>

                    {/* Email and Phone */}
                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        name="email"
                        placeholder="Email*"
                        type="email"
                        value={values.email}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.email && Boolean(errors.email)}
                        helperText={touched.email && errors.email}
                        required
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#FFFFFF',
                            borderRadius: 2,
                            '& input::placeholder': {
                              color: '#404040',
                              opacity: 1,
                              fontWeight: 500
                            },
                            '&:hover': {
                              backgroundColor: '#ffffff'
                            },
                            '&.Mui-focused': {
                              backgroundColor: '#ffffff'
                            }
                          }
                        }}
                      />
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <TextField
                        fullWidth
                        name="phone"
                        placeholder="Phone"
                        type="tel"
                        value={values.phone}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.phone && Boolean(errors.phone)}
                        helperText={touched.phone && errors.phone}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#FFFFFF',
                            borderRadius: 2,
                            '& input::placeholder': {
                              color: '#404040',
                              opacity: 1,
                              fontWeight: 500
                            },
                            '&:hover': {
                              backgroundColor: '#ffffff'
                            },
                            '&.Mui-focused': {
                              backgroundColor: '#ffffff'
                            }
                          }
                        }}
                      />
                    </Grid>

                    {/* How Did You Hear About Us */}
                    <Grid item xs={12}>
                      <FormControl fullWidth>
                        <Select
                          name="howDidYouHear"
                          value={values.howDidYouHear}
                          onChange={handleChange}
                          onBlur={handleBlur}
                          displayEmpty
                          error={touched.howDidYouHear && Boolean(errors.howDidYouHear)}
                          sx={{
                            backgroundColor: '#FFFFFF',
                            borderRadius: 2,
                            '& .MuiSelect-placeholder': {
                              color: '#404040',
                              opacity: 1
                            }
                          }}
                        >
                          <MenuItem value="" disabled sx={{ color: '#404040', fontWeight: 500 }}>
                            How Did You Hear About Us?
                          </MenuItem>
                          <MenuItem value="Google Search">Google Search</MenuItem>
                          <MenuItem value="Social Media">Social Media</MenuItem>
                          <MenuItem value="Referral">Referral</MenuItem>
                          <MenuItem value="Advertisement">Advertisement</MenuItem>
                          <MenuItem value="Other">Other</MenuItem>
                        </Select>
                        {touched.howDidYouHear && errors.howDidYouHear && <FormHelperText error>{errors.howDidYouHear}</FormHelperText>}
                      </FormControl>
                    </Grid>

                    {/* Subject */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="subject"
                        placeholder="Subject"
                        value={values.subject}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.subject && Boolean(errors.subject)}
                        helperText={touched.subject && errors.subject}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#FFFFFF',
                            borderRadius: 2,
                            '& input::placeholder': {
                              color: '#404040',
                              opacity: 1,
                              fontWeight: 500
                            },
                            '&:hover': {
                              backgroundColor: '#ffffff'
                            },
                            '&.Mui-focused': {
                              backgroundColor: '#ffffff'
                            }
                          }
                        }}
                      />
                    </Grid>

                    {/* Message */}
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        name="message"
                        placeholder="Message"
                        multiline
                        rows={4}
                        value={values.message}
                        onChange={handleChange}
                        onBlur={handleBlur}
                        error={touched.message && Boolean(errors.message)}
                        helperText={touched.message && errors.message}
                        sx={{
                          '& .MuiOutlinedInput-root': {
                            backgroundColor: '#FFFFFF',
                            borderRadius: 2,
                            '& textarea::placeholder': {
                              color: '#404040',
                              opacity: 1,
                              fontWeight: 500
                            },
                            '&:hover': {
                              backgroundColor: '#ffffff'
                            },
                            '&.Mui-focused': {
                              backgroundColor: '#ffffff'
                            }
                          }
                        }}
                      />
                    </Grid>

                    {/* Submit Button */}
                    <Grid item xs={12}>
                      <Stack direction="row" justifyContent="flex-end" sx={{ mt: 2 }}>
                        <Button
                          type="submit"
                          variant="contained"
                          size="large"
                          disabled={isSubmitting}
                          sx={{
                            borderRadius: 2,
                            fontWeight: 700,
                            fontSize: '1rem',
                            textTransform: 'none',
                            px: 5,
                            // py: 1.5,
                            background: 'linear-gradient(135deg, #000000 0%, #7c3aed 100%)',
                            boxShadow: '0 4px 16px rgba(139, 92, 246, 0.3)',
                            '&:hover': {
                              background: 'linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%)',
                              boxShadow: '0 6px 20px rgba(139, 92, 246, 0.4)',
                              transform: 'translateY(-1px)'
                            },
                            '&:disabled': {
                              background: '#e2e8f0',
                              color: '#94a3b8'
                            }
                          }}
                        >
                          {isSubmitting ? 'Sending...' : 'Send'}
                        </Button>
                      </Stack>
                    </Grid>
                  </Grid>
                </Form>
              )}
            </Formik>
          </CardContent>
        </Card>
      </Box>
    </Box>
  );
}

export default ContactUs;
