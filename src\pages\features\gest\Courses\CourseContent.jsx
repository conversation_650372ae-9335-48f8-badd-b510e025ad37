import { TabPanel } from '@mui/lab';
import { Box, Divider, List, ListItem, Tab, Tabs, Typography } from '@mui/material';
import { useState } from 'react';

const CourseContent = () => {
  const [value, setValue] = useState(0);
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };
  const courseContet = [
    'Build a career as a Digital Marketing Specialist by leveraging cloud-based tools and platforms.',
    'Gain professional-level training from Google, focusing on marketing technologies powered by the cloud',
    'Showcase your skills through portfolio-ready projects that demonstrate your cloud-enabled marketing strategies',
    'Earn an employer-recognized certificate from Google, validating your expertise in cloud-supported digital marketing.',
    'Become eligible for in-demand roles such as Marketing Coordinator,E-commerce Associate, and Paid Search Specialist, where cloud services are key to success'
  ];
  return (
    <Box pb={8}>
      <Tabs onChange={handleChange} value={value} selectionFollowsFocus={false}>
        <Tab label="COURSE CONTENT" />
        <Divider orientation="vertical" flexItem variant="middle" sx={{ borderWidth: '1px', borderColor: '#999', height: 17, mt: 1.8 }} />
        <Tab label="OUTCOMES" />
        <Divider orientation="vertical" flexItem variant="middle" sx={{ borderWidth: '1px', borderColor: '#999', height: 17, mt: 1.8 }} />
        <Tab label="PREREQUISITES" />
      </Tabs>
      <Box>
        {value == 0 && (
          <List>
            {courseContet.map((content) => (
              <ListItem>
                <Box sx={{ width: 8, height: 8, borderRadius: 50, backgroundColor: '#8C76DD', mx: 2 }}> </Box>
                <Typography variant="h5" fontWeight={400}>
                  {content}
                </Typography>
              </ListItem>
            ))}
          </List>
        )}
      </Box>
    </Box>
  );
};

export default CourseContent;
