import { Box, List, ListItem, ListItemIcon, ListItemText, Stack, Typography } from '@mui/material';
import HomeSections from '../HomePage/components/common/HomeSections';
import CourseContent from './CourseContent';

import CourseCard from '../HomePage/components/CoursesSection/CourseCard';
import { FormattedMessage } from 'react-intl';
const CourseDetails = () => {
  const descCourse = [
    'Understand the fundamental elements of cloud computing.',
    'Filter out the hype and focus on real cloud value',
    'Explore different cloud service models like IaaS, PaaS, and SaaS.',
    'Learn how businesses use cloud solutions to innovate and grow',
    'Get introduced to top cloud platforms like AWS, Azure, and Google Cloud.'
  ];
  return (
    <HomeSections>
      <Stack sx={{ flexDirection: 'row', justifyContent: 'space-between', alignItems: 'flex-satrt', pt: 10, px: 4 }}>
        <Box>
          <Typography variant="h1" fontWeight={900}>
            Introduction to Cloud Computing
          </Typography>
          <Typography variant="h3" fontWeight={400} my={4}>
            Learn How to Master Cloud Computing
          </Typography>

          <Stack flexDirection={'row'}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Typography variant="h7" fontWeight={700} ml={1}>
                <FormattedMessage id="sections" /> :
              </Typography>
              <Typography variant="h7" fontWeight={700} color="#808080" mx={0.5}>
                10
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Typography variant="h7" fontWeight={700} color="#808080" ml={1}>
                <FormattedMessage id="lectures" /> :
              </Typography>
              <Typography variant="h7" fontWeight={700} color="#808080" mx={0.5}>
                20
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Typography variant="h7" fontWeight={700} color="#808080" ml={1}>
                <FormattedMessage id="session-length" />:
              </Typography>
              <Typography variant="h7" fontWeight={700} color="#808080" mx={0.5}>
                25m
              </Typography>
            </Box>
          </Stack>
          <Stack flexDirection={'row'} mt={1}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Typography variant="h7" fontWeight={700} ml={1}>
                <FormattedMessage id="last-updated" />:
              </Typography>
              <Typography variant="h7" fontWeight={700} color="#808080" mx={0.5}>
                4/2025
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Typography variant="h7" fontWeight={700} color="#808080" ml={1}>
                <FormattedMessage id="language" />:
              </Typography>
              <Typography variant="h7" fontWeight={700} color="#808080" mx={0.5}>
                Arabic
              </Typography>
            </Box>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center'
              }}
            >
              <Typography variant="h7" fontWeight={700} color="#808080" ml={1}>
                <FormattedMessage id="subtitle" />:
              </Typography>
              <Typography variant="h7" fontWeight={700} color="#808080" mx={0.5}>
                English
              </Typography>
            </Box>
          </Stack>

          <Box
            my={5}
            p={2}
            width={'80%'}
            sx={{
              my: 5,
              p: 3,
              width: '80%',
              backgroundColor: '#ddd8f3'
            }}
          >
            <Typography variant="h5" fontWeight={400} width={'70%'}>
              This course is part of the Google Cloud Computing Professional Certificate. When you enroll in this course, you'll also be
              enrolled in this Professional Certificate.
            </Typography>
            <List>
              {descCourse.map((desc) => (
                <ListItem sx={{ padding: 0 }}>
                  <ListItemIcon>{/* <IconSVG iconPath={icons.icon2} color="#39B54A" width={15} height={15} /> */}</ListItemIcon>
                  <ListItemText>{desc}</ListItemText>
                </ListItem>
              ))}
            </List>
          </Box>

          <CourseContent />
        </Box>
        <Box>
          {/* <Card sx={{ maxWidth: 400, p: 1.5, borderRadius: 2 }}>
            <CardActionArea>
              <CardMedia component="img" sx={{ borderRadius: 1 }} image={Course} alt="green iguana" />
              <CardContent sx={{ p: 0 }}>
                <Stack
                  direction="row"
                  spacing={1.2}
                  alignItems="center"
                  justifyContent={'center'}
                  sx={{
                    paddingTop: 0.5,
                    paddingBottom: 0.5,
                    paddingLeft: 0,
                    paddingRight: 0,
                    borderTop: '1px solid #000',
                    borderBottom: '1px solid #000',
                    my: 5
                  }}
                >
                  <Box display={'flex'} flexDirection="row" alignItems="center">
                    <Typography variant="h7" color="#999" mx={0.8}>
                      Free
                    </Typography>
                  </Box>
                  <Divider
                    orientation="vertical"
                    flexItem
                    variant="middle"
                    style={{ borderWidth: '1px', borderColor: '#999', height: 13, marginTop: 5 }}
                  />
                  <Box display={'flex'} flexDirection="row" alignItems="center">
                    <ArchiveBook size="15" color="#000" variant="Outline" />
                    <Typography variant="h7" fontWeight={700} color="#000" mx={0.8}>
                      65
                    </Typography>
                  </Box>
                  <Divider
                    orientation="vertical"
                    flexItem
                    variant="middle"
                    style={{ borderWidth: '1px', borderColor: '#999', height: 13, marginTop: 5 }}
                  />
                  <Box display={'flex'} flexDirection="row" alignItems="center">
                    <User size="15" color="#000" variant="Outline" />
                    <Typography variant="h7" fontWeight={700} color="#000" mx={0.8}>
                      255
                    </Typography>
                  </Box>
                  <Divider
                    orientation="vertical"
                    flexItem
                    variant="middle"
                    style={{ borderWidth: '1px', borderColor: '#999', height: 13, marginTop: 5 }}
                  />
                  <Box display={'flex'} flexDirection="row" alignItems="center">
                    <Typography variant="h7" fontWeight={700} color="#000" mx={0.8}>
                      (30)
                    </Typography>
                    <Rating name="half-rating-read" defaultValue={3.5} precision={5} readOnly size="small" />
                  </Box>
                </Stack>
              </CardContent>
            </CardActionArea>
          </Card> */}
          <CourseCard price={253} books={65} trainees={230} />
        </Box>
      </Stack>
    </HomeSections>
  );
};

export default CourseDetails;
