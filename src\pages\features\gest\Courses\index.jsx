import { Box, Stack, Typography } from '@mui/material';
import Pagination from '@mui/material/Pagination';
import TitleSections from '../HomePage/components/common/TitleSections';
import CourseCard from '../HomePage/components/CoursesSection/CourseCard';
import { useState } from 'react';
import { Category } from 'iconsax-react';
import HomeSections from '../HomePage/components/common/HomeSections';

const Courses = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [page, setPage] = useState(1);

  const handleChange = (event, value) => {
    setPage(value);
  };
  const handleTabChange = (index) => {
    setActiveTab(index);
  };
  const courseDetails = [
    {
      id: 1,
      title: 'Data Science',
      price: 500.55,
      books: 40,
      trainees: 140
    },
    {
      id: 2,
      title: 'Introduction to Cloud Computing',
      price: 0,
      books: 21,
      trainees: 285
    },
    {
      id: 3,
      title: 'Fundementals of Ethics & Data Privacy',
      price: 655.72,
      books: 25,
      trainees: 775
    },
    {
      id: 4,
      title: 'Learn Data Structure',
      price: 0,
      books: 69,
      trainees: 354
    },
    {
      id: 5,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 6,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 7,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 8,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 9,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    }
  ];
  return (
    <HomeSections>
      <Box sx={{ position: 'relative', pt: 10 }}>
        <Box px={3} pr={8}>
          <TitleSections
            title={'COURSES'}
            subTitle={'Your Learning Journey'}
            desc={
              'Explore our diverse courses and fnd the perfect ft for your goals! Embark on a learning journey that empowers you with knowledge, skills, and endless opportunities!'
            }
          />

          <Stack direction="row" spacing={5} alignItems={'center'} my={4}>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 0 ? '#000' : '#F7F5D7'}
              color={activeTab === 0 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(0)}
            >
              DATA SCIENCE
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 1 ? '#000' : '#F7F5D7'}
              color={activeTab === 1 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(1)}
            >
              ARTIFICIAL INTELLEGENCE
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 2 ? '#000' : '#F7F5D7'}
              color={activeTab === 2 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(2)}
            >
              MACHINE LEARNING
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 3 ? '#000' : '#F7F5D7'}
              color={activeTab === 3 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(3)}
            >
              WEB DEVELOPMENT
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 4 ? '#000' : '#F7F5D7'}
              color={activeTab === 4 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(4)}
            >
              CYBERSECURITY
            </Typography>
            <Box
              style={{ cursor: 'pointer' }}
              display={'flex'}
              flexDirection={'row'}
              justifyContent={'space-between'}
              alignItems={'center'}
            >
              <Category size="15" color="#000" variant="Linear" />
              <Typography variant="h6" fontWeight={700} mx={1}>
                ALL CATEGORIES
              </Typography>
            </Box>
          </Stack>
        </Box>

        <Stack direction={'row'} alignItems={'center'} width={'93%'} rowGap={5} columnGap={2} mx={'auto'} flexWrap={'wrap'}>
          {courseDetails.map((course, index) => (
            <Box width={'31.5%'}>
              <CourseCard title={course.title} price={course.price} books={course.books} trainees={course.trainees} id={course.id} />
            </Box>
          ))}
        </Stack>
        <Box sx={{ my: 10, mx: 'auto', width: '50%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Pagination
            count={10}
            page={page}
            onChange={handleChange}
            sx={{
              '& .MuiPaginationItem-root': {
                '&.Mui-selected': {
                  // Style for the selected page
                  backgroundColor: '#8C76DD',
                  color: 'white'
                }
              }
            }}
            size="large"
            shape="circular"
          />
        </Box>
      </Box>
    </HomeSections>
  );
};

export default Courses;
