import { Box, Typography } from '@mui/material';
import TitleSections from '../HomePage/components/common/TitleSections';

const FaqPage = () => {
  const faqList = [
    {
      title: '1. What is Naahel 360?',
      answer:
        'Naahel 360 is a ready-to-use awareness and training platform designed for organizations seeking high-quality educational content in different areas for personal and professional development.'
    },
    {
      title: '2. Can I request a demo before subscribing?',
      answer:
        'Yes, we offer a free demo session where you can explore the platform, its features, and the available content. Just fill out the demo request form on our website or contact our team directly.'
    },
    {
      title: '3. What types of content does Naahel 360 provide?',
      answer:
        'Naahel 360 includes educational programs, training courses, learning pathways. The content is ready to use once you selected or customizable based on your needs.'
    },
    {
      title: '4. Do users receive certificates after completing courses?',
      answer: 'Yes, users receive official certificates upon successful completion of each course or training program.'
    },
    {
      title: '5. How can I subscribe to Naahel 360?',
      answer:
        'You can subscribe by contacting our sales team or submitting your request through the website. Subscription packages vary based on the number of users and required courses.'
    },
    {
      title: '6. Is the platform customizable?',
      answer:
        'Yes, we offer customization options including branding, adding specific content, user roles, and tailored reporting based on your organization’s requirements.'
    }
  ];
  return (
    <Box bgcolor={'#fff'} pt={10} px={5} pb={2} mt={5} borderRadius={4}>
      <Box>
        <TitleSections
          title={'GOT QUESTIONS?'}
          subTitle={'We’ve Got Answers'}
          desc={
            'We provide the support you need. Discover how partnering with Naahel 360 can unlock innovative solutions and open new opportunities for your organization to thrive.'
          }
        />
      </Box>
      <Box mt={5} bgcolor={'#e6e6e6'} py={3} px={5} borderRadius={3} mb={2}>
        <Box width={'80%'}>
          <Box>
            <Typography variant="h3" fontWeight={300} pb={1} mb={2} borderBottom={'1px solid #000'}>
              FREQUENTLY ASKED QUESTIONS (FAQ) – NAAHEL 360
            </Typography>
            <Typography variant="h5" fontWeight={700}>
              We are here to guide you through everything you need to know — from our services to how we can help your business grow and
              thrive. Discover how partnering with Naahel 360 can unlock new opportunities for your organization.
            </Typography>
          </Box>
          <Box mt={3}>
            {faqList.map((faqItem, index) => (
              <Box mb={3} pt={3} borderTop={'1px solid #fff'}>
                <Typography variant="h5" fontWeight={300}>
                  {faqItem.title}
                </Typography>
                <Typography variant="h5" fontWeight={700}>
                  {faqItem.answer}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default FaqPage;
