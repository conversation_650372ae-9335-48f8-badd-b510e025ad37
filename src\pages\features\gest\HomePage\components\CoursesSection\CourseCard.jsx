import { Box, <PERSON>ton, Card, CardActions, CardContent, CardMedia, Divider, Rating, Stack, SvgIcon, Typography } from '@mui/material';
import Course from 'assets/images/course.png';
import { ArchiveBook, Heart, ShoppingCart, User } from 'iconsax-react';
import { useState } from 'react';
import { ToastContainer, toast } from 'react-toastify';
// import icons from 'data/icons';
import IconSVG from 'components/IconSVG';
import { useNavigate } from 'react-router';
import { FormattedMessage } from 'react-intl';
const CourseCard = ({ title, price, books, trainees, id }) => {
  const [myFavorite, setMyFavorite] = useState(false);

  const navigate = useNavigate();
  const handleFavorite = () => {
    setMyFavorite(!myFavorite);
  };
  console.log('title', title);

  return (
    <Box sx={{ marginLeft: 2, width: title ? 'auto' : '100%' }}>
      <Card sx={{ padding: 1, position: 'relative', borderRadius: 2 }}>
        <CardMedia sx={{ height: 230, borderRadius: 2 }} image={Course} title="green iguana" />

        <Box
          sx={{
            width: 28,
            height: 28,
            borderRadius: '50%',
            position: 'absolute',
            top: 13,
            right: 13,
            bgcolor: '#fff',
            borderColor: '#000',
            borderWidth: 2,
            borderStyle: 'solid',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer'
          }}
          onClick={handleFavorite}
        >
          {myFavorite ? <Heart size="20" color="#f47373" variant="Bold" /> : <Heart size="20" color="#000" />}
        </Box>
        <CardContent
          style={{
            padding: 0,
            marginBottom: 20
          }}
        >
          <Typography
            variant="h3"
            noWrap={false}
            onClick={() => navigate(`/courses/${id}`)}
            sx={{
              height: title ? 58 : 0,
              mb: 3,
              fontWeight: 700,
              cursor: 'pointer'
            }}
          >
            {title}
          </Typography>

          <Stack
            direction="row"
            spacing={title ? 0 : 1}
            alignItems="center"
            justifyContent={'center'}
            sx={{
              paddingTop: 0,
              paddingBottom: 0,
              paddingLeft: 0,
              paddingRight: 0,
              borderTop: '1px solid #000',
              borderBottom: '1px solid #000',
              my: title ? 0 : 4
            }}
          >
            <Box display={'flex'} flexDirection="row" alignItems="center">
              {price != 0 ? (
                <>
                  <IconSVG width={15} height={17} color="black" />
                  <Typography variant="h7" fontWeight={700} color="#000" mx={0.8}>
                    {price}
                  </Typography>
                </>
              ) : (
                <Typography variant="h7" color="#999" mx={0.8}>
                  Free
                </Typography>
              )}
            </Box>
            <Divider
              orientation="vertical"
              flexItem
              variant="middle"
              style={{ borderWidth: '1px', borderColor: '#999', height: 13, marginTop: 5 }}
            />
            <Box display={'flex'} flexDirection="row" alignItems="center">
              <ArchiveBook size="15" color="#000" variant="Outline" />
              <Typography variant="h7" fontWeight={700} color="#000" mx={0.8}>
                {books}
              </Typography>
            </Box>
            <Divider
              orientation="vertical"
              flexItem
              variant="middle"
              style={{ borderWidth: '1px', borderColor: '#999', height: 13, marginTop: 5 }}
            />
            <Box display={'flex'} flexDirection="row" alignItems="center">
              <User size="15" color="#000" variant="Outline" />
              <Typography variant="h7" fontWeight={700} color="#000" mx={0.8}>
                {trainees}
              </Typography>
            </Box>
            <Divider
              orientation="vertical"
              flexItem
              variant="middle"
              style={{ borderWidth: '1px', borderColor: '#999', height: 13, marginTop: 5 }}
            />
            <Box display={'flex'} flexDirection="row" alignItems="center">
              <Typography variant="h7" fontWeight={700} color="#000" mx={0.8}>
                (30)
              </Typography>
              <Rating name="half-rating-read" defaultValue={3.5} precision={5} readOnly size="small" />
            </Box>
          </Stack>
        </CardContent>
        <CardActions style={{ padding: 0, marginBottom: 3, flexDirection: title ? 'row' : 'column' }}>
          <Button
            variant="contained"
            fullWidth={true}
            style={{ borderRadius: 50, backgroundColor: '#000' }}
            startIcon={<ShoppingCart size="16" color="#fff" variant="Bold" />}
          >
            <Typography variant="h7" fontWeight={700} color="#fff" mx={1}>
              <FormattedMessage id="add-to-cart" />
            </Typography>
          </Button>
          {!title && (
            <Button
              variant="contained"
              fullWidth={true}
              sx={{ borderRadius: 50, backgroundColor: '#8C76DD', mt: 2, ml: '0 !important', mb: 2 }}
            >
              <Typography variant="h7" fontWeight={700} color="#fff" mx={1}>
                <FormattedMessage id="explore-related-course" />
              </Typography>
            </Button>
          )}
        </CardActions>
      </Card>
      <ToastContainer />
    </Box>
  );
};

export default CourseCard;
