import { Box, Divider, Stack, Typography } from '@mui/material';
import { Category } from 'iconsax-react';
import { useState } from 'react';

import CourseCard from './CourseCard';

import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import { FreeMode, Pagination } from 'swiper/modules';
import TitleSections from '../common/TitleSections';
import HomeSections from '../common/HomeSections';
import { useNavigate } from 'react-router';
import { FormattedMessage } from 'react-intl';
const CoursesSection = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (index) => {
    setActiveTab(index);
  };
  const courseDetails = [
    {
      id: 1,
      title: 'Data Science',
      price: 500.55,
      books: 40,
      trainees: 140
    },
    {
      id: 2,
      title: 'Introduction to Cloud Computing',
      price: 0,
      books: 21,
      trainees: 285
    },
    {
      id: 3,
      title: 'Fundementals of Ethics & Data Privacy',
      price: 655.72,
      books: 25,
      trainees: 775
    },
    {
      id: 4,
      title: 'Learn Data Structure',
      price: 0,
      books: 69,
      trainees: 354
    },
    {
      id: 5,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 6,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 7,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 8,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    },
    {
      id: 9,
      title: 'Introduction to Cloud Computing',
      price: 245.9,
      books: 36,
      trainees: 124
    }
  ];

  return (
    <HomeSections>
      <Box sx={{ position: 'relative' }}>
        <Box px={3} pr={8}>
          <TitleSections
            title="courses"
            subTitle={'Your Learning Journey'}
            desc={
              'Explore our diverse courses and fnd the perfect ft for your goals! Embark on a learning journey that empowers you with knowledge, skills, and endless opportunities!'
            }
          />

          <Stack direction="row" spacing={5} alignItems={'center'} my={4}>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 0 ? '#000' : '#F7F5D7'}
              color={activeTab === 0 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(0)}
            >
              DATA SCIENCE
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 1 ? '#000' : '#F7F5D7'}
              color={activeTab === 1 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(1)}
            >
              ARTIFICIAL INTELLEGENCE
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 2 ? '#000' : '#F7F5D7'}
              color={activeTab === 2 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(2)}
            >
              MACHINE LEARNING
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 3 ? '#000' : '#F7F5D7'}
              color={activeTab === 3 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(3)}
            >
              WEB DEVELOPMENT
            </Typography>
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 4 ? '#000' : '#F7F5D7'}
              color={activeTab === 4 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(4)}
            >
              CYBERSECURITY
            </Typography>
            <Box
              style={{ cursor: 'pointer' }}
              display={'flex'}
              flexDirection={'row'}
              justifyContent={'space-between'}
              alignItems={'center'}
            >
              <Category size="15" color="#000" variant="Linear" />
              <Typography variant="h6" fontWeight={700} mx={1}>
                ALL CATEGORIES
              </Typography>
            </Box>
          </Stack>
        </Box>

        <Box
          sx={{
            '& .swiper-pagination-bullet': {
              backgroundColor: 'transparent',
              border: '1px solid #AAA',
              width: 18,
              height: 18,
              opacity: 0.5
            },
            '& .swiper-pagination-bullet-active': {
              backgroundColor: '#000',
              opacity: 1
            }
          }}
        >
          <Swiper
            slidesPerView={4}
            spaceBetween={0}
            freeMode={true}
            pagination={{
              clickable: true,
              bulletActiveClass: 'swiper-pagination-bullet-active'
            }}
            modules={[FreeMode, Pagination]}
            style={{ paddingBottom: 70 }}
            className="mySwiper"
          >
            {courseDetails.map((course, index) => (
              <SwiperSlide key={index}>
                <CourseCard title={course.title} price={course.price} books={course.books} trainees={course.trainees} id={course.id} />
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>
        <Box sx={{ position: 'absolute', right: 30, bottom: 10, zIndex: 99, cursor: 'pointer' }} onClick={() => navigate('/courses')}>
          <Typography variant="h4" fontWeight={700}>
            <FormattedMessage id="show-all-courses" />
          </Typography>
        </Box>
      </Box>
    </HomeSections>
  );
};

export default CoursesSection;
