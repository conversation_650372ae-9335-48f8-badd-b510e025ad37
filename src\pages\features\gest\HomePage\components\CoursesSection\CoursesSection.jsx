import { Box, Stack, Typography, CircularProgress, Alert } from '@mui/material';
import { Category } from 'iconsax-react';
import { useState, useEffect } from 'react';

import CourseCard from './CourseCard';

import { Swiper, SwiperSlide } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import { FreeMode, Pagination } from 'swiper/modules';
import TitleSections from '../common/TitleSections';
import HomeSections from '../common/HomeSections';
import { useNavigate } from 'react-router';
import { FormattedMessage } from 'react-intl';

// Import API function and tenant context
import { getTenantCourses } from '../../../../../../services/multiTenantApi';
import { useTenant } from '../../../../../../contexts/TenantContext';
const CoursesSection = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);

  // State management for courses
  const [courses, setCourses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get current tenant
  const { currentTenant } = useTenant();

  const handleTabChange = (index) => {
    setActiveTab(index);
  };

  // Load courses from API
  useEffect(() => {
    const loadCourses = async () => {
      if (!currentTenant?.id) return;

      try {
        setLoading(true);
        setError(null);

        const response = await getTenantCourses(currentTenant.id, 'ar', 10);

        // Transform API data to match component structure
        const transformedCourses =
          response?.courses?.map((course) => ({
            id: course.id,
            title: course.name,
            price: course.finalprice === 'Free' ? 0 : parseFloat(course.finalprice) || 0,
            books: 0, // Default value - API doesn't provide this
            trainees: 0, // Default value - API doesn't provide this
            category: course.category?.[0]?.name || 'General',
            rating: course.rating || 0,
            language: course.language,
            level: course.level,
            logo: course.logo,
            startdate: course.startdate,
            enddate: course.enddate,
            trainers: course.trainers || [],
            approval_required: course.approval_required
          })) || [];

        setCourses(transformedCourses);
        setCategories(response?.categories || []);
      } catch (err) {
        console.error('Error loading courses:', err);
        setError(err.message);

        // Fallback to static data on error
        setCourses([
          {
            id: 1,
            title: 'Data Science',
            price: 500.55,
            books: 40,
            trainees: 140
          },
          {
            id: 2,
            title: 'Introduction to Cloud Computing',
            price: 0,
            books: 21,
            trainees: 285
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadCourses();
  }, [currentTenant?.id]);

  // Show loading state
  if (loading) {
    return (
      <HomeSections>
        <Box sx={{ position: 'relative' }}>
          <Box px={3} pr={8}>
            <TitleSections
              title="courses"
              subTitle={'Your Learning Journey'}
              desc={
                'Explore our diverse courses and find the perfect fit for your goals! Embark on a learning journey that empowers you with knowledge, skills, and endless opportunities!'
              }
            />
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        </Box>
      </HomeSections>
    );
  }

  return (
    <HomeSections>
      <Box sx={{ position: 'relative' }}>
        <Box px={3} pr={8}>
          <TitleSections
            title="courses"
            subTitle={'Your Learning Journey'}
            desc={
              'Explore our diverse courses and find the perfect fit for your goals! Embark on a learning journey that empowers you with knowledge, skills, and endless opportunities!'
            }
          />

          {/* Show error alert if there's an error */}
          {error && (
            <Box sx={{ mb: 2 }}>
              <Alert severity="warning">تم تحميل البيانات الاحتياطية بسبب خطأ في الاتصال: {error}</Alert>
            </Box>
          )}

          <Stack direction="row" spacing={5} alignItems={'center'} my={4}>
            {/* All Courses Tab */}
            <Typography
              variant="h6"
              fontWeight={700}
              bgcolor={activeTab === 0 ? '#000' : '#F7F5D7'}
              color={activeTab === 0 ? '#fff' : '#000'}
              py={1}
              px={3}
              style={{ cursor: 'pointer' }}
              borderRadius={4}
              onClick={() => handleTabChange(0)}
            >
              ALL COURSES
            </Typography>

            {/* Dynamic Categories from API */}
            {categories.slice(0, 4).map((category, index) => (
              <Typography
                key={category.id}
                variant="h6"
                fontWeight={700}
                bgcolor={activeTab === index + 1 ? '#000' : '#F7F5D7'}
                color={activeTab === index + 1 ? '#fff' : '#000'}
                py={1}
                px={3}
                style={{ cursor: 'pointer' }}
                borderRadius={4}
                onClick={() => handleTabChange(index + 1)}
              >
                {category.name.toUpperCase()}
              </Typography>
            ))}

            {/* Fallback static categories if no API categories */}
            {categories.length === 0 && (
              <>
                <Typography
                  variant="h6"
                  fontWeight={700}
                  bgcolor={activeTab === 1 ? '#000' : '#F7F5D7'}
                  color={activeTab === 1 ? '#fff' : '#000'}
                  py={1}
                  px={3}
                  style={{ cursor: 'pointer' }}
                  borderRadius={4}
                  onClick={() => handleTabChange(1)}
                >
                  DATA SCIENCE
                </Typography>
                <Typography
                  variant="h6"
                  fontWeight={700}
                  bgcolor={activeTab === 2 ? '#000' : '#F7F5D7'}
                  color={activeTab === 2 ? '#fff' : '#000'}
                  py={1}
                  px={3}
                  style={{ cursor: 'pointer' }}
                  borderRadius={4}
                  onClick={() => handleTabChange(2)}
                >
                  WEB DEVELOPMENT
                </Typography>
              </>
            )}
            <Box
              style={{ cursor: 'pointer' }}
              display={'flex'}
              flexDirection={'row'}
              justifyContent={'space-between'}
              alignItems={'center'}
            >
              <Category size="15" color="#000" variant="Linear" />
              <Typography variant="h6" fontWeight={700} mx={1}>
                ALL CATEGORIES
              </Typography>
            </Box>
          </Stack>
        </Box>

        <Box
          sx={{
            '& .swiper-pagination-bullet': {
              backgroundColor: 'transparent',
              border: '1px solid #AAA',
              width: 18,
              height: 18,
              opacity: 0.5
            },
            '& .swiper-pagination-bullet-active': {
              backgroundColor: '#000',
              opacity: 1
            }
          }}
        >
          <Swiper
            slidesPerView={4}
            spaceBetween={0}
            freeMode={true}
            pagination={{
              clickable: true,
              bulletActiveClass: 'swiper-pagination-bullet-active'
            }}
            modules={[FreeMode, Pagination]}
            style={{ paddingBottom: 70 }}
            className="mySwiper"
          >
            {courses.map((course, index) => (
              <SwiperSlide key={course.id || index}>
                <CourseCard
                  title={course.title}
                  price={course.price}
                  books={course.books}
                  trainees={course.trainees}
                  id={course.id}
                  category={course.category}
                  rating={course.rating}
                  logo={course.logo}
                />
              </SwiperSlide>
            ))}
          </Swiper>
        </Box>
        <Box sx={{ position: 'absolute', right: 30, bottom: 10, zIndex: 99, cursor: 'pointer' }} onClick={() => navigate('/courses')}>
          <Typography variant="h4" fontWeight={700}>
            <FormattedMessage id="show-all-courses" />
          </Typography>
        </Box>
      </Box>
    </HomeSections>
  );
};

export default CoursesSection;
