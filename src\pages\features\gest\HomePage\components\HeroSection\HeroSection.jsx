import { Box, Typography } from '@mui/material';
import { Swiper, SwiperSlide } from 'swiper/react';

// Import Swiper styles
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';

import { Autoplay, Pagination, Navigation } from 'swiper/modules';
const HeroSection = () => {
  return (
    <Box my={5} style={{ height: '70vh' }}>
      <Swiper className="mySwiper" style={{ height: '100%' }}>
        <SwiperSlide style={{ width: '100%', display: 'flex', justifyContent: 'end', flexDirection: 'column' }}>
          <Typography variant="h1" fontSize={65} fontWeight={200}>
            Grow Your Skills
          </Typography>
          <Typography variant="h1" fontSize={65} fontWeight={900}>
            Transform Your Future
          </Typography>
        </SwiperSlide>
      </Swiper>
    </Box>
  );
};

export default HeroSection;
