import { Box, Typography } from '@mui/material';
import Stepper from '@mui/material/Stepper';
import Step from '@mui/material/Step';
import StepLabel from '@mui/material/StepLabel';
import { RotateLeft } from 'iconsax-react';
const PathMaterials = () => {
  const truncateString = (str, maxLength = 50) => {
    if (str.length > maxLength) {
      return str.substring(0, maxLength) + '...';
    }
    return str;
  };
  const steps = [
    'A Day In The Life of A Data Scientist',
    'Te Non-Technical Skills of Effective Data Scientists',
    'Data Science and Analytics Career Paths & Certifications: First Steps',
    'Data Science Foundations: Fundamentals (2022)',
    'Statistics Foundations 1: Te Basics',
    'Statistics Foundations 2: Probability'
  ];
  return (
    <Box display={'flex'} flexDirection={'row'} alignItems={'center'} position={'relative'}>
      <Box bgcolor={'#eae6ff'} sx={{ borderTopLeftRadius: 15, borderBottomLeftRadius: 15 }} p={2} width={'95.1%'}>
        <Box sx={{ width: '100%' }}>
          <Stepper
            activeStep={0}
            alternativeLabel
            sx={{
              '.MuiStepConnector-line': { borderColor: '#fff' },
              '.MuiStepIcon-root': { color: '#fff' },
              '& .Mui-active': { '&.MuiStepIcon-root': { color: '#fff' } }
              // '& .Mui-completed': { '&.MuiStepIcon-root': { color: '#fff' }, '&.MuiStepConnector-line': { borderColor: '#fff' } }
            }}
          >
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>
                  <Typography variant="h7" fontWeight={700}>
                    {truncateString(label)}
                  </Typography>
                </StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>
      </Box>

      <Box
        width={112}
        position={'absolute'}
        right={0}
        bgcolor={'#eae6ff'}
        sx={{ borderBottomRightRadius: 15, borderBottomLeftRadius: 15, transform: 'rotate(-90deg)' }}
        p={2}
      >
        <Typography variant="h7" fontWeight={700} fontSize={12}>
          LEARN MORE..
        </Typography>
      </Box>
    </Box>
  );
};

export default PathMaterials;
