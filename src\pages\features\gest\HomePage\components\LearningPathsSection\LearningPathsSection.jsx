import { Box, Divider, Stack, Typography } from '@mui/material';
import TitleSections from '../common/TitleSections';
import LearningPathCard from './LearningPathCard/LearningPathCard';

import { Mousewheel, Pagination } from 'swiper/modules';
import { SwiperSlide, Swiper } from 'swiper/react';
import 'swiper/css';
import 'swiper/css/pagination';
import HomeSections from '../common/HomeSections';

const LearningPathsSection = () => {
  const pathCards = [
    { id: 1, index: 0, title: 'Frontend Development' },
    { id: 2, index: 1, title: 'Backend Development' },
    { id: 3, index: 2, title: 'Full Stack Development' },
    { id: 4, index: 3, title: 'Data Science' }
  ];
  return (
    <HomeSections>
      <Box px={3} pr={8}>
        <TitleSections
          title={'learning-paths'}
          subTitle={'Build a Solid Foundation'}
          desc={
            'Master in-demand skills with our expertly designed programs! Gain hands-on experience, boost your confidence, and take your career to new heights.'
          }
        />
      </Box>

      {pathCards.map((card) => (
        <Box width={'97%'} mx="auto" my={5} key={card.id}>
          <LearningPathCard title={card.title} />
        </Box>
      ))}
    </HomeSections>
  );
};

export default LearningPathsSection;
