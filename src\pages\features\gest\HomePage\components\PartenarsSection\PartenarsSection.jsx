import { Box, ImageList, ImageListItem, Stack, Typography } from '@mui/material';
import adobe from 'assets/images/partenars/adobe.png';
import learning from 'assets/images/partenars/elearning.png';
import national from 'assets/images/partenars/education.png';
import technique from 'assets/images/partenars/technique.png';
import windows from 'assets/images/partenars/window.png';
import { FormattedMessage } from 'react-intl';

const PartenarsSection = () => {
  const itemData = [
    {
      img: adobe,
      title: 'Adobe'
    },
    {
      img: learning,
      title: 'Learning'
    },
    {
      img: national,
      title: 'National'
    },
    {
      img: technique,
      title: 'Technique'
    },
    {
      img: windows,
      title: 'Windows'
    },
    {
      img: adobe,
      title: 'Adobe'
    },
    {
      img: learning,
      title: 'Learning'
    },
    {
      img: national,
      title: 'National'
    },
    {
      img: technique,
      title: 'Technique'
    },
    {
      img: windows,
      title: 'Windows'
    },
    {
      img: adobe,
      title: 'Adobe'
    },
    {
      img: learning,
      title: 'Learning'
    },
    {
      img: national,
      title: 'National'
    },
    {
      img: technique,
      title: 'Technique'
    },
    {
      img: windows,
      title: 'Windows'
    },
    {
      img: adobe,
      title: 'Adobe'
    },
    {
      img: learning,
      title: 'Learning'
    },
    {
      img: national,
      title: 'National'
    },
    {
      img: technique,
      title: 'Technique'
    },
    {
      img: windows,
      title: 'Windows'
    },
    {
      img: adobe,
      title: 'Adobe'
    },
    {
      img: learning,
      title: 'Learning'
    },
    {
      img: national,
      title: 'National'
    },
    {
      img: technique,
      title: 'Technique'
    }
  ];
  return (
    <Box mb={7}>
      <Box textAlign={'center'} mb={8}>
        <Typography component={'span'} variant="h2" fontWeight={800} pb={1} mx={'auto'} borderBottom={'1px solid #000'}>
          <FormattedMessage id="trusted-by" />
        </Typography>
      </Box>
      <Stack direction={'row'} alignItems={'center'} width={'80%'} rowGap={5} columnGap={15} mx={'auto'} flexWrap={'wrap'}>
        {itemData.map((item, index) => (
          <img
            key={index}
            srcSet={`${item.img}?w=164&h=164&fit=crop&auto=format&dpr=2 2x`}
            src={`${item.img}?w=164&h=164&fit=crop&auto=format`}
            alt={item.title}
            loading="lazy"
            style={{ width: 70, height: 70 }}
          />
        ))}
      </Stack>
    </Box>
  );
};

export default PartenarsSection;
