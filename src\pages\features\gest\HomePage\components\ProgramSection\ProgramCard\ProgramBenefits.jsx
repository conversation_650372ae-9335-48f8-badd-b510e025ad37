import { Accordion, AccordionDetails, AccordionSummary, Box, Divider, Typography } from '@mui/material';

export default function ProgramBenefits() {
  return (
    <Box
      overflow={'auto'}
      maxHeight={500}
      sx={{
        '&::-webkit-scrollbar': { width: 10 },
        '&::-webkit-scrollbar-thumb': { backgroundColor: '#000', borderRadius: 50 },
        '&::-webkit-scrollbar-track': { backgroundColor: '#CCC', borderRadius: 50 }
      }}
    >
      <Box mb={2} px={4}>
        <Typography pb={1} fontWeight={700}>
          PROFESSIONAL CERTIFICATE- 7 COURSE SERIES
        </Typography>
        <Divider component={'span'} style={{ backgroundColor: '#000', borderColor: '#000', width: '100%', display: 'block' }} />
        <Typography fontSize={12} fontWeight={400}>
          Prepare for a career in the high-growth field of UX design, no experience or degree required. With professional training designed
          by Google, get on the fast-track to a competitively paid job.{' '}
        </Typography>
      </Box>

      <Box>
        <Accordion style={{ backgroundColor: 'rgb(230, 230, 230)' }} square>
          <AccordionSummary aria-controls="panel2-content" id="panel2-header" style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
            <Box>
              <Typography component="div" fontWeight={900}>
                Foundations of User Experience (UX) Design
              </Typography>
              <Typography component="span" fontSize={10}>
                COURSE 1 (13)
              </Typography>
              <Typography component="span" fontSize={10} color="#808080" px={0.5}>
                Hours • 4.8(190 Ratings)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
          </AccordionDetails>
        </Accordion>
        <Accordion style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
          <AccordionSummary aria-controls="panel2-content" id="panel2-header" style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
            <Box>
              <Typography component="div" fontWeight={600}>
                Start the UX Design Process: Empathize, Define, and Ideate
              </Typography>
              <Typography component="span" fontSize={10}>
                COURSE 1 (13)
              </Typography>
              <Typography component="span" fontSize={10} color="#808080" px={0.5}>
                Hours • 4.8(190 Ratings)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
          </AccordionDetails>
        </Accordion>
        <Accordion style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
          <AccordionSummary aria-controls="panel2-content" id="panel2-header" style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
            <Box>
              <Typography component="div" fontWeight={600}>
                Build Wireframes and Low-Fidelity Prototypes
              </Typography>
              <Typography component="span" fontSize={10}>
                COURSE 1 (13)
              </Typography>
              <Typography component="span" fontSize={10} color="#808080" px={0.5}>
                Hours • 4.8(190 Ratings)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
          </AccordionDetails>
        </Accordion>
        <Accordion style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
          <AccordionSummary aria-controls="panel2-content" id="panel2-header" style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
            <Box>
              <Typography component="div" fontWeight={600}>
                Conduct UX Research and Test Early Concepts
              </Typography>
              <Typography component="span" fontSize={10}>
                COURSE 1 (13)
              </Typography>
              <Typography component="span" fontSize={10} color="#808080" px={0.5}>
                Hours • 4.8(190 Ratings)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
          </AccordionDetails>
        </Accordion>
        <Accordion style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
          <AccordionSummary aria-controls="panel2-content" id="panel2-header" style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
            <Box>
              <Typography component="div" fontWeight={600}>
                Create High-Fidelity Designs and Prototypes in Figma
              </Typography>
              <Typography component="span" fontSize={10}>
                COURSE 1 (13)
              </Typography>
              <Typography component="span" fontSize={10} color="#808080" px={0.5}>
                Hours • 4.8(190 Ratings)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
          </AccordionDetails>
        </Accordion>
        <Accordion style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
          <AccordionSummary aria-controls="panel2-content" id="panel2-header" style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
            <Box>
              <Typography component="div" fontWeight={600}>
                Build Dynamic User Interfaces (UI) for Websites
              </Typography>
              <Typography component="span" fontSize={10}>
                COURSE 1 (13)
              </Typography>
              <Typography component="span" fontSize={10} color="#808080" px={0.5}>
                Hours • 4.8(190 Ratings)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
          </AccordionDetails>
        </Accordion>

        <Accordion style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
          <AccordionSummary aria-controls="panel2-content" id="panel2-header" style={{ backgroundColor: 'rgb(230, 230, 230)' }}>
            <Box>
              <Typography component="div" fontWeight={600}>
                Design a User Experience for Social Good & Prepare for Jobs
              </Typography>
              <Typography component="span" fontSize={10}>
                COURSE 1 (13)
              </Typography>
              <Typography component="span" fontSize={10} color="#808080" px={0.5}>
                Hours • 4.8(190 Ratings)
              </Typography>
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Suspendisse malesuada lacus ex, sit amet blandit leo lobortis eget.
          </AccordionDetails>
        </Accordion>
      </Box>
    </Box>
  );
}
