import { Box, Divider, Rating, SvgIcon, Typography, Button, Stack } from '@mui/material';
import { Heart } from 'iconsax-react';
import ProgramBenfits from './ProgramBenefits';
import ProgramFeatures from './ProgramFeatures';

import { useState } from 'react';
const ProgramCard = ({ program }) => {
  const { programType, programName, programCourses, programDuration, programHours, rating, numberVotes, skills } = program;
  const [myFavorite, setMyFavorite] = useState(false);
  const handleFavorite = () => {
    setMyFavorite(!myFavorite);
  };
  return (
    <Box
      bgcolor={'#f5f5f5'}
      p={1}
      pt={6}
      borderRadius={2}
      width={'81%'}
      boxShadow={'3px 8px 12px rgb(0, 0, 0)'}
      my={5}
      mx="auto"
      position={'relative'}
    >
      <Box
        sx={{
          width: 28,
          height: 28,
          borderRadius: '50%',
          position: 'absolute',
          top: 13,
          right: 13,
          bgcolor: '#fff',
          borderColor: '#000',
          borderWidth: 1,
          borderStyle: 'solid',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          cursor: 'pointer'
        }}
        onClick={handleFavorite}
      >
        {myFavorite ? <Heart size="20" color="#f47373" variant="Bold" /> : <Heart size="20" color="#000" />}
      </Box>
      <Box display="flex" flexDirection={'column'} justifyContent="center" alignItems={'center'} mb={3} mx="auto">
        <Box textAlign={'center'} mb={2}>
          <Typography variant="h5" fontWeight={200}>
            {programType.toUpperCase()}
          </Typography>
          <Typography variant="h1" fontWeight={700}>
            {programName}
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 4, width: '100%', justifyContent: 'center' }}>
          <Divider
            orientation="horizontal"
            flexItem
            variant="middle"
            style={{ borderWidth: '1px', borderColor: '#999', height: '50%', width: '33%', marginTop: 10 }}
          />
          <Typography variant="h5" fontWeight={500}>
            {programCourses} COURSES SERIES
          </Typography>
          <Divider
            orientation="horizontal"
            flexItem
            variant="middle"
            style={{ borderWidth: '1px', borderColor: '#999', height: '50%', width: '33%', marginTop: 10 }}
          />
        </Box>
      </Box>

      <Box display="flex" justifyContent={'space-between'} alignItems="start" mb={3} mx={8}>
        <Box width={'53%'} bgcolor={'rgb(230, 230, 230)'} px={4} py={3} borderRadius={1}>
          <ProgramBenfits />
        </Box>
        <Box width={'35%'}>
          <ProgramFeatures
            programDuration={programDuration}
            programHours={programHours}
            rating={rating}
            numberVotes={numberVotes}
            skills={skills}
          />
        </Box>
      </Box>
    </Box>
  );
};

export default ProgramCard;
