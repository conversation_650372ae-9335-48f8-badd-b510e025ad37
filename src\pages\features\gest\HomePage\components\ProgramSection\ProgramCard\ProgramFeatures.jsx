import { <PERSON>, Divider, <PERSON>ing, SvgIcon, Typography, Button, Stack } from '@mui/material';
// import { CheckCircle, Star, AutoAwesome, ShoppingCart } from '@mui/icons-material';
import { useState } from 'react';
const ProgramFeatures = () => {
  const [value, setValue] = useState(2);
  return (
    <Box>
      <Typography fontWeight={400}>FOR BEGINNERS</Typography>
      <Box mt={4} mb={1}>
        <Typography component={'span'} fontWeight={'bold'} fontSize={20}>
          6 months
        </Typography>
        <Typography component={'span'} fontWeight={'bold'} fontSize={15} px={1}>
          (10 hours/week)
        </Typography>
      </Box>
      <Typography component={'span'} fontSize={15}>
        SCHEDULE
      </Typography>
      <Box my={4}>
        <Typography component={'div'} fontWeight={'bold'} fontSize={20} mb={1}>
          10,992
        </Typography>
        <Typography component={'span'} fontSize={15}>
          ALREADY ENROLLED
        </Typography>
      </Box>
      <Box mt={4} mb={5}>
        <Rating
          name="simple-controlled"
          value={value}
          onChange={(event, newValue) => {
            setValue(newValue);
          }}
        />
        <Typography component={'span'} fontSize={15} mt={-2}>
          (891) votes
        </Typography>
        <Typography component={'div'} fontWeight={500} fontSize={16} mb={1}>
          RATING
        </Typography>
      </Box>

      <Box>
        <Stack direction="row" spacing={2} flexWrap="wrap" useFlexGap>
          {/* Certified by Google Button */}
          <Button
            variant="outlined"
            sx={{
              backgroundColor: '#8C76DD',
              color: 'white',
              textTransform: 'none',
              borderColor: 'grey.300',

              '&:hover': {
                borderColor: 'grey.400'
              }
            }}
          >
            <Typography variant="body2" fontWeight="medium">
              CERTIFIED BY GOOGLE
            </Typography>
          </Button>

          {/* Top Instructor Button */}
          <Button
            variant="outlined"
            sx={{
              backgroundColor: '#8C76DD',
              color: 'white',
              textTransform: 'none',
              borderColor: 'grey.300',

              '&:hover': {
                borderColor: 'grey.400'
              }
            }}
          >
            <Typography variant="body2" fontWeight="medium">
              TOP INSTRUCTOR
            </Typography>
          </Button>

          {/* New AI Skills Button */}
          <Button
            variant="outlined"
            // startIcon={<AutoAwesome color="white" />}
            sx={{
              backgroundColor: '#8C76DD',
              color: 'white',
              textTransform: 'none',
              borderColor: 'grey.300',

              '&:hover': {
                borderColor: 'grey.400'
              }
            }}
          >
            <Typography variant="body2" fontWeight="medium">
              NEW AI SKILLS
            </Typography>
          </Button>

          {/* KSA Certified Button */}
          <Button
            variant="outlined"
            sx={{
              backgroundColor: '#8C76DD',
              color: 'white',
              textTransform: 'none',
              borderColor: 'grey.300',

              '&:hover': {
                borderColor: 'grey.400'
              }
            }}
          >
            <Typography variant="body2" fontWeight="medium">
              KSA CERTIFIED
            </Typography>
          </Button>

          {/* More Button */}
          <Button
            variant="text"
            sx={{
              textTransform: 'none',
              color: 'text.secondary'
            }}
          >
            <Typography variant="body2">more...</Typography>
          </Button>
        </Stack>
      </Box>
      <Box mt={4}>
        <Stack direction="row" spacing={2}>
          {/* ADD TO CART Button */}
          <Button
            variant="contained"
            // startIcon={<ShoppingCart />}
            sx={{
              textTransform: 'none',
              fontWeight: 'bold',
              width: '60%',
              px: 3,

              backgroundColor: 'black',
              color: 'white',
              '&:hover': {
                backgroundColor: 'primary.dark'
              }
            }}
          >
            ADD TO CART
          </Button>

          {/* Learn more... Button */}
          <Button
            variant="text"
            sx={{
              textTransform: 'none',
              color: 'black',
              width: '35%',
              fontWeight: 'medium',
              '&:hover': {
                backgroundColor: 'transparent',
                textDecoration: 'underline'
              }
            }}
          >
            Learn more...
          </Button>
        </Stack>
      </Box>
    </Box>
  );
};

export default ProgramFeatures;
