import { Box, Divider, Stack, Typography } from '@mui/material';
import TitleSections from '../common/TitleSections';
import ProgramCard from './ProgramCard/ProgramCard';

import { Swiper, SwiperSlide } from 'swiper/react';
import { EffectCoverflow, Pagination } from 'swiper/modules';
import HomeSections from '../common/HomeSections';

const ProgramSection = () => {
  const programDetails = [
    {
      programType: 'PROFESSIONAL CERTIFICATE',
      programName: 'Google UX Design',
      programCourses: 7,
      programDuration: '6 months',
      programHours: 10,
      trainees: 10992,
      rating: 4,
      numberVotes: 800,
      skills: ['Certificate By Google', 'Top Instructor', 'New AI Skills', 'KSA Certified', 'Career Support']
    },
    {
      programType: 'PROFESSIONAL CERTIFICATE',
      programName: 'Programming Development',
      programCourses: 9,
      programDuration: '8 months',
      programHours: 15,
      trainees: 8659,
      rating: 3,
      numberVotes: 500,
      skills: ['Certificate By Google', 'Top Instructor', 'New AI Skills']
    },
    {
      programType: 'PROFESSIONAL CERTIFICATE',
      programName: 'Backend Development',
      programCourses: 4,
      programDuration: '3 months',
      programHours: 4,
      trainees: 9124,
      rating: 5,
      numberVotes: 366,
      skills: ['Certificate By Google', 'Top Instructor']
    }
  ];

  return (
    <HomeSections>
      <Box px={3} pr={8}>
        <TitleSections
          title={'programs'}
          subTitle={'Earn a Career Certificate'}
          desc={
            'Master in-demand skills with our expertly designed programs! Gain hands-on experience, boost your confidence, and take your career to new heights.'
          }
        />
      </Box>
      <Box
        sx={{
          '& .swiper-pagination-bullet': {
            backgroundColor: 'transparent',
            border: '1px solid #AAA',
            width: 18,
            height: 18,
            opacity: 0.5
          },
          '& .swiper-pagination-bullet-active': {
            backgroundColor: '#000',
            opacity: 1
          }
        }}
      >
        <Swiper
          effect={'coverflow'}
          grabCursor={true}
          centeredSlides={true}
          slidesPerView={'auto'}
          coverflowEffect={{
            rotate: 50,
            stretch: 0,
            depth: 100,
            modifier: 1,
            slideShadows: true
          }}
          style={{ paddingBottom: 70 }}
          pagination={true}
          modules={[EffectCoverflow, Pagination]}
          className="mySwiper"
        >
          {programDetails.map((program, index) => (
            <SwiperSlide key={index}>
              <ProgramCard program={program} />
            </SwiperSlide>
          ))}
        </Swiper>
      </Box>
    </HomeSections>
  );
};

export default ProgramSection;
