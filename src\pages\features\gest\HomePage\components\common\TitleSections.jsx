import { Box, Divider, Stack, Typography, useTheme } from '@mui/material';
import { FormattedMessage } from 'react-intl';

const TitleSections = ({ title, subTitle, desc }) => {
  const theme = useTheme();

  const tenantColors = {
    primary: theme.palette.primary.main,
    secondary: theme.palette.secondary.main,
    tertiary: theme.palette.tertiary?.main || theme.palette.info.main
  };
  return (
    <>
      <Typography variant="h6" fontWeight={700} sx={{ color: tenantColors.primary }}>
        <FormattedMessage id={title} />
      </Typography>
      <Stack
        direction="row"
        divider={
          <Divider
            orientation="vertical"
            flexItem
            variant="middle"
            sx={{
              borderWidth: '2px',
              borderColor: tenantColors.secondary,
              opacity: 0.7
            }}
          />
        }
        spacing={4}
        alignItems={'center'}
        mt={-1}
      >
        <Box width={'75%'}>
          <Typography
            variant="h1"
            fontWeight={900}
            sx={{
              background: `linear-gradient(45deg, ${tenantColors.primary}, ${tenantColors.tertiary})`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text'
            }}
          >
            {subTitle}
          </Typography>
        </Box>
        <Typography variant="h5" fontWeight={500} sx={{ color: tenantColors.secondary }}>
          {desc}
        </Typography>
      </Stack>
    </>
  );
};

export default TitleSections;
