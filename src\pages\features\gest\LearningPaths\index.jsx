import { Box, Pagination, Stack, Typography } from '@mui/material';
import HomeSections from '../HomePage/components/common/HomeSections';
import { useState } from 'react';
import TitleSections from '../HomePage/components/common/TitleSections';
import LearningPathCard from '../HomePage/components/LearningPathsSection/LearningPathCard/LearningPathCard';

const LearningPaths = () => {
  const [page, setPage] = useState(1);

  const handleChange = (event, value) => {
    setPage(value);
  };
  const pathCards = [{ index: 0 }, { index: 1 }, { index: 2 }, { index: 3 }];
  return (
    <HomeSections bgColor={false}>
      <Box sx={{ position: 'relative', pt: 10 }}>
        <Box px={3} pr={8}>
          <TitleSections
            title={'learning-paths'}
            subTitle={'Build a Solid Foundation'}
            desc={
              'Discover tailored learning paths designed to elevate your skills and accelerate your growth. Choose your path, and step confidently into your future!'
            }
          />
        </Box>

        {pathCards.map((card, index) => (
          <>
            <Box width={'97%'} mx="auto" my={5} key={index}>
              <LearningPathCard />
            </Box>
          </>
        ))}
        <Box sx={{ my: 10, mx: 'auto', width: '50%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Pagination
            count={10}
            page={page}
            onChange={handleChange}
            sx={{
              '& .MuiPaginationItem-root': {
                '&.Mui-selected': {
                  // Style for the selected page
                  backgroundColor: '#8C76DD',
                  color: 'white'
                }
              }
            }}
            size="large"
            shape="circular"
          />
        </Box>
      </Box>
    </HomeSections>
  );
};

export default LearningPaths;
