import React, { useState } from 'react';
import { <PERSON>, <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ing, Grid, IconButton, Collapse } from '@mui/material';
import { ArrowDown2, Heart, ShoppingCart } from 'iconsax-react';
import img from 'assets/images/image.png';

export default function ProgramDetails() {
  const [favorites, setFavorites] = useState(new Set());
  const [expandedCourses, setExpandedCourses] = useState(new Set());

  const programs = [
    {
      id: 1,
      title: 'Google UX Design',
      type: 'PROFESSIONAL CERTIFICATE',
      series: '7 COURSES SERIES',
      description:
        'The Google UX Design program is a professional certificate designed to equip learners with the essential skills needed to start a career in user experience (UX) design. Developed by Google experts, the course covers the full UX design process, from user research and wireframing to creating high-fidelity prototypes. No previous experience is required, making it accessible to beginners. Throughout the program, students work on hands-on projects, building a professional portfolio that showcases their abilities to potential employers.',
      level: 'FOR BEGINNERS',
      duration: '6 months',
      timePerWeek: '10 hours/week',
      enrolled: '10,992',
      rating: 4.5,
      votes: 891,
      badges: [
        { label: 'CERTIFIED BY GOOGLE', color: 'primary' },
        { label: 'TOP INSTRUCTOR', color: 'secondary' },
        { label: 'NEW AT SKILLS', color: 'success' },
        { label: 'KSA CERTIFIED', color: 'info' }
      ],
      image: img,
      prerequisites: [
        "Bachelor's degree in Computer Science, Information Technology, or a related field.",
        'Familiarity with programming languages (e.g., Python, Java, or C++).',
        'Basic knowledge of networking and cybersecurity fundamentals.',
        'Experience with operating systems (Windows, Linux).',
        'Completion of an introductory course in data structures and algorithms.',
        'Strong problem-solving and analytical skills.',
        'Personal laptop meeting minimum technical specifications.'
      ],
      courses: [
        'Foundations of User Experience (UX) Design',
        'Start the UX Design Process: Empathize, Define, and Ideate',
        'Build Wireframes and Low-Fidelity Prototypes',
        'Conduct UX Research and Test Early Concepts',
        'Create High-Fidelity Designs and Prototypes in Figma',
        'Build Dynamic User Interfaces (UI) for Websites',
        'Design a User Experience for Social Good & Prepare for Jobs'
      ]
    }
  ];

  const toggleFavorite = (programId) => {
    const newFavorites = new Set(favorites);
    if (newFavorites.has(programId)) {
      newFavorites.delete(programId);
    } else {
      newFavorites.add(programId);
    }
    setFavorites(newFavorites);
  };

  const toggleCourseExpansion = (programId) => {
    const newExpanded = new Set(expandedCourses);
    if (newExpanded.has(programId)) {
      newExpanded.delete(programId);
    } else {
      newExpanded.add(programId);
    }
    setExpandedCourses(newExpanded);
  };

  return (
    <Box
      sx={{
        minHeight: '100vh'
        // mx: 4,
        // my: 4
      }}
    >
      {/* Programs Grid */}
      <Box sx={{ mx: 5 }}>
        <Grid container spacing={4}>
          {programs.map((program) => (
            <Grid item xs={12} key={program.id}>
              <Card
                sx={{
                  borderRadius: 2,
                  boxShadow: '0 8px 32px rgba(139, 92, 246, 0.15)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  background: 'rgba(255, 255, 255, 0.95)',
                  backdropFilter: 'blur(10px)',
                  transition: 'all 0.3s ease',
                  position: 'relative',
                  overflow: 'visible',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: '0 12px 40px rgba(139, 92, 246, 0.25)'
                  }
                }}
              >
                {/* Favorite Button */}
                <Box sx={{ display: 'flex', justifyContent: 'end', m: 2 }}>
                  <IconButton
                    onClick={() => toggleFavorite(program.id)}
                    sx={{
                      zIndex: 2,
                      backgroundColor: 'rgba(255, 255, 255, 0.9)',
                      '&:hover': {
                        backgroundColor: 'rgba(255, 255, 255, 1)'
                      }
                    }}
                  >
                    <Heart
                      variant={favorites.has(program.id) ? 'Bold' : 'Outline'}
                      color={favorites.has(program.id) ? '#ef4444' : '#64748b'}
                      size="24"
                    />
                  </IconButton>
                </Box>
                <Box sx={{ textAlign: 'start', mx: 4 }}>
                  <Typography
                    variant="overline"
                    sx={{
                      fontWeight: 300,
                      letterSpacing: 1.5
                    }}
                  >
                    {program.type}
                  </Typography>

                  <Typography
                    variant="h3"
                    sx={{
                      fontWeight: 900,
                      color: '#1e293b',
                      fontSize: { xs: '1.8rem', md: '2.2rem' }
                    }}
                  >
                    {program.title}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mx: 4 }}>
                  <Typography
                    variant="overline"
                    sx={{
                      fontWeight: 400,
                      letterSpacing: 1.5
                    }}
                  >
                    {program.type}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mx: 4 }}>
                  <Box sx={{ flex: 1, height: '2px', bgcolor: 'text.secondary', opacity: 0.9 }} />

                  <Typography
                    variant="overline"
                    sx={{
                      fontWeight: 500,
                      letterSpacing: 1.5,
                      mx: 2
                    }}
                  >
                    {program.series}
                  </Typography>
                  <Box sx={{ flex: 1, height: '2px', bgcolor: 'text.secondary', opacity: 0.9 }} />
                </Box>
                <CardContent sx={{ p: 4 }}>
                  <Stack direction={{ xs: 'column', lg: 'row' }} spacing={4} alignItems="flex-start">
                    {/* Left: Introduction, Prerequisites, and Course List */}
                    <Box sx={{ flex: 1, backgroundColor: '', p: 2, borderRadius: 2 }}>
                      {/* Introduction Section */}
                      <Typography variant="h5" sx={{ fontWeight: 700, mb: 1 }}>
                        Introduction
                      </Typography>
                      <Typography variant="body1" sx={{ color: '#475569', mb: 2, lineHeight: 1.6, fontSize: '0.95rem' }}>
                        {program.description}
                      </Typography>

                      {/* Outcomes | Program Prerequisites Section */}
                      <Box sx={{ mt: 3 }}>
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'satart',
                            gap: 2,
                            mb: 0.5,
                            pb: 0.5,
                            borderBottom: '2px solid #404040'
                          }}
                        >
                          <Typography variant="overline" sx={{ fontWeight: 700, letterSpacing: 2, fontSize: '1rem' }}>
                            OUTCOMES
                          </Typography>
                          <Typography
                            variant="overline"
                            sx={{
                              fontWeight: 700,
                              letterSpacing: 2,
                              color: '#404040',
                              fontSize: '1rem',
                              borderLeft: '1px solid #404040',
                              pl: 2
                            }}
                          >
                            PROGRAM PREREQUISITES
                          </Typography>
                        </Box>

                        <Typography variant="body2" sx={{ color: '#475569', mb: 1 }}>
                          Prepare for a career in the high-growth field of UX design, no experience or degree required. With professional
                          training designed by Google, get on the fast-track to a competitively paid job.
                        </Typography>
                        <ul style={{ margin: 0, paddingLeft: 20 }}>
                          {program.prerequisites.map((item, idx) => (
                            <li key={idx} style={{ marginBottom: 8, color: '#22223b', fontSize: '0.98rem' }}>
                              {item}
                            </li>
                          ))}
                        </ul>
                      </Box>
                      <Box sx={{ flex: 1, backgroundColor: '#ddd6fe73', p: 2, borderRadius: 2 }}>
                        {/* Course List with Scroll */}
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'space-between',
                            mb: 0.5,
                            pb: 0.5,
                            borderBottom: '2px solid #404040'
                          }}
                        >
                          <Typography
                            variant="overline"
                            sx={{
                              fontWeight: 700,
                              color: '#1e293b',
                              letterSpacing: 2,
                              fontSize: '0.875rem'
                            }}
                          >
                            {program.type} - {program.series}
                          </Typography>
                        </Box>

                        {/* Description */}
                        <Typography
                          variant="body1"
                          sx={{
                            color: '#475569',
                            mb: 1,
                            lineHeight: 1.6,
                            fontSize: '0.95rem'
                          }}
                        >
                          {program.description}
                        </Typography>
                        <Box
                          sx={{
                            maxHeight: 400,
                            overflowY: 'auto',
                            pr: 1,
                            mt: 3,
                            '&::-webkit-scrollbar': {
                              width: '6px'
                            },
                            '&::-webkit-scrollbar-track': {
                              backgroundColor: '#f1f5f9',
                              borderRadius: '3px'
                            },
                            '&::-webkit-scrollbar-thumb': {
                              backgroundColor: '#404040',
                              borderRadius: '3px',
                              '&:hover': {
                                backgroundColor: '#404040'
                              }
                            }
                          }}
                        >
                          {program.courses.map((course, idx) => (
                            <Box
                              key={idx}
                              sx={{
                                borderBottom: idx < program.courses.length - 1 ? '1px solid #f1f5f9' : 'none',
                                py: 2,
                                px: 1,
                                '&:hover': {
                                  backgroundColor: '#f8fafc',
                                  borderRadius: 1
                                }
                              }}
                            >
                              <Stack direction="row" alignItems="flex-start" justifyContent="space-between" spacing={2}>
                                <Box sx={{ flex: 1 }}>
                                  <Typography
                                    variant="h6"
                                    sx={{
                                      color: '#1e293b',
                                      fontWeight: 600,
                                      fontSize: '1rem',
                                      mb: 0.5,
                                      lineHeight: 1.4
                                    }}
                                  >
                                    {course}
                                  </Typography>
                                  <Stack direction="row" alignItems="center" spacing={2}>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        color: '#64748b',
                                        fontWeight: 600,
                                        textTransform: 'uppercase',
                                        letterSpacing: 1
                                      }}
                                    >
                                      COURSE {idx + 1}
                                    </Typography>
                                    <Typography variant="caption" sx={{ color: '#64748b' }}>
                                      ({Math.floor(Math.random() * 20) + 10}) Hours
                                    </Typography>
                                    <Stack direction="row" alignItems="center" spacing={0.5}>
                                      <Typography variant="caption" sx={{ color: '#64748b', fontWeight: 600 }}>
                                        4.8
                                      </Typography>
                                      <Box
                                        sx={{
                                          width: 12,
                                          height: 12,
                                          backgroundColor: '#fbbf24',
                                          borderRadius: '50%',
                                          display: 'flex',
                                          alignItems: 'center',
                                          justifyContent: 'center'
                                        }}
                                      >
                                        <Typography sx={{ color: 'white', fontSize: '8px', fontWeight: 'bold' }}>★</Typography>
                                      </Box>
                                      <Typography variant="caption" sx={{ color: '#64748b' }}>
                                        ({Math.floor(Math.random() * 500) + 100} Ratings)
                                      </Typography>
                                    </Stack>
                                  </Stack>
                                </Box>
                                {/* Expand Arrow */}
                                <IconButton
                                  size="small"
                                  sx={{
                                    color: '#64748b',
                                    '&:hover': {
                                      backgroundColor: '#f1f5f9'
                                    }
                                  }}
                                >
                                  <ArrowDown2 variant="Bold" color="#64748b" size="20" />
                                </IconButton>
                              </Stack>
                            </Box>
                          ))}
                        </Box>
                      </Box>
                    </Box>

                    {/* Right: Info & Actions */}
                    <Box sx={{ minWidth: { xs: '100%', lg: 350 } }}>
                      <Stack spacing={3}>
                        {/* Program Image */}
                        <Box sx={{ width: '100%', mb: 2, borderRadius: 2, overflow: 'hidden', position: 'relative' }}>
                          <img
                            src={program.image}
                            alt={program.title}
                            style={{ width: '100%', height: '180px', objectFit: 'cover', borderRadius: '12px' }}
                          />
                        </Box>
                        <Typography variant="body1" sx={{ fontWeight: 500, letterSpacing: 3 }}>
                          {program.level}
                        </Typography>

                        <Typography>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Typography
                              variant="h3"
                              sx={{
                                fontWeight: 800,
                                color: '#1e293b'
                              }}
                            >
                              {program.duration}
                            </Typography>
                            <Typography
                              variant="body1"
                              sx={{
                                color: '#1e293b'
                              }}
                            >
                              ({program.timePerWeek})
                            </Typography>
                          </Stack>
                          <Typography
                            variant="caption"
                            sx={{
                              textTransform: 'uppercase',
                              letterSpacing: 3,
                              display: 'block',
                              mt: 0.5
                            }}
                          >
                            SCHEDULE
                          </Typography>
                        </Typography>

                        <Box>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Typography
                              variant="h3"
                              sx={{
                                fontWeight: 800,
                                color: '#1e293b'
                              }}
                            >
                              {program.enrolled}
                            </Typography>
                          </Stack>
                          <Typography
                            variant="caption"
                            sx={{
                              textTransform: 'uppercase',
                              letterSpacing: 3,
                              display: 'block',
                              mt: 0.5
                            }}
                          >
                            ALREADY ENROLLED
                          </Typography>
                        </Box>

                        <Box>
                          <Stack direction="row" alignItems="center" spacing={1}>
                            <Rating
                              value={program.rating}
                              precision={4.5}
                              readOnly
                              size="large"
                              sx={{
                                '& .MuiRating-iconFilled': {
                                  color: '#fbbf24',
                                  svg: {
                                    stroke: '#333',
                                    strokeWidth: 1
                                  }
                                },
                                '& .MuiRating-iconEmpty': {
                                  color: '#e0e0e0',
                                  svg: {
                                    stroke: '#333',
                                    strokeWidth: 1
                                  }
                                }
                              }}
                            />
                            <Typography variant="body2">({program.votes} votes)</Typography>
                          </Stack>
                          <Typography
                            variant="caption"
                            sx={{
                              textTransform: 'uppercase',
                              letterSpacing: 3,
                              display: 'block',
                              mt: 0.5
                            }}
                          >
                            RATING
                          </Typography>
                        </Box>

                        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                          {program.badges.map((badge, idx) => (
                            <Chip
                              key={idx}
                              label={badge.label}
                              color="primary"
                              size="small"
                              sx={{
                                fontWeight: 600,
                                fontSize: '0.7rem'
                              }}
                            />
                          ))}
                        </Stack>
                        <Box sx={{ display: 'flex', gap: 1, width: '100%' }}>
                          <Button
                            variant="contained"
                            color="primary"
                            size="large"
                            startIcon={<ShoppingCart variant="Bold" color="white" size="20" />}
                            sx={{
                              flex: 1,
                              borderRadius: 2,
                              fontWeight: 700,
                              fontSize: '1rem',
                              textTransform: 'none',
                              background: '#404040',
                              boxShadow: '0 4px 16px rgba(107, 70, 193, 0.3)',
                              '&:hover': {
                                background: 'linear-gradient(135deg, #553c9a 0%, #7c3aed 100%)',
                                boxShadow: '0 6px 20px rgba(107, 70, 193, 0.4)',
                                transform: 'translateY(-1px)'
                              }
                            }}
                          >
                            ADD TO CART
                          </Button>

                          <Button
                            variant="text"
                            sx={{
                              color: '#404040',
                              textTransform: 'none',
                              fontWeight: 600,
                              borderRadius: 3,
                              whiteSpace: 'nowrap',
                              px: 2,
                              minWidth: 0
                            }}
                          >
                            Learn more...
                          </Button>
                        </Box>
                      </Stack>
                    </Box>
                  </Stack>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
}
