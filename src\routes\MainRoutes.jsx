import { lazy } from 'react';

// project-imports
import Loadable from 'components/Loadable';
import { SimpleLayoutType } from 'config';
import DashboardLayout from 'layout/Dashboard';
import SimpleLayout from 'layout/Simple';
// import Trainings from '../pages/admin/Trainings';
// import Courses from '../pages/admin/Trainings/Courses';

// render - admin pages
const Dashboard = Loadable(lazy(() => import('pages/features/admin/AfterLogin/Dashboard')));
const Home = Loadable(lazy(() => import('pages/features/gest/HomePage/HomePage.jsx')));
const Programs = Loadable(lazy(() => import('pages/features/gest/Programs/Programs.jsx')));
const ProgramDetails = Loadable(lazy(() => import('pages/features/gest/Programs/ProgramDetails')));
const ContactUS = Loadable(lazy(() => import('pages/features/gest/ContactUs/ContactUs.jsx')));
const Courses = Loadable(lazy(() => import('pages/features/admin/AfterLogin/Trainings/Courses')));
const CoursesPage = Loadable(lazy(() => import('pages/features/gest/Courses')));
const CoursesDetails = Loadable(lazy(() => import('pages/features/gest/Courses/CourseDetails.jsx')));
const LearningPaths = Loadable(lazy(() => import('pages/features/gest/LearningPaths')));
// const SamplePage = Loadable(lazy(() => import('pages/features/extra-pages/sample-page')));
const AboutUs = Loadable(lazy(() => import('pages/features/gest/AboutUsPage/AboutUsPage.jsx')));
const Faq = Loadable(lazy(() => import('pages/features/gest/FaqPage/FaqPage.jsx')));
// const TenantDashboard = Loadable(lazy(() => import('pages/TenantDashboard')));
// ==============================|| MAIN ROUTES ||============================== //

const MainRoutes = {
  path: '/',
  children: [
    {
      path: '/',
      element: <SimpleLayout layout={SimpleLayoutType.SIMPLE} />,
      children: [
        {
          path: 'home',
          element: <Home />
        },

        {
          path: 'programs',
          children: [
            {
              path: '',
              element: <Programs />
            },
            {
              path: ':id',
              element: <ProgramDetails />
            }
          ]
        },
        {
          path: 'courses',
          children: [
            {
              path: '',
              element: <CoursesPage />
            },
            {
              path: ':id',
              element: <CoursesDetails />
            }
          ]
        },
        {
          path: 'learningpaths',
          children: [
            {
              path: '',
              element: <LearningPaths />
            }
          ]
        },
        {
          path: 'contact-us',
          element: <ContactUS />
        },
        {
          path: 'faq',
          element: <Faq />
        },
        {
          path: 'about-us',
          element: <AboutUs />
        },
        {
          path: 'tenant-dashboard'
          // element: <TenantDashboard />
        }
      ]
    },

    {
      path: 'dashboard',
      element: <DashboardLayout />,
      children: [
        {
          path: '',
          element: <Dashboard />
        },
        {
          path: 'trainings',
          children: [
            {
              path: 'myCourses',
              element: <Courses />
            },
            {
              path: 'classrooms',
              element: <div>Classrooms Page</div>
            },
            {
              path: 'paths',
              element: <div>Paths Page</div>
            },
            {
              path: 'programs',
              element: <div>Programs Page</div>
            }
          ]
        },

        {
          path: 'exams',
          element: <div>Exams Page</div>
        },
        {
          path: 'requests',
          element: <div>Requests Page</div>
        },
        {
          path: 'certificates',
          element: <div>Certificates Page</div>
        },
        {
          path: 'payments',
          element: <div>Payments Page</div>
        }
      ]
    }
  ]
};

export default MainRoutes;
