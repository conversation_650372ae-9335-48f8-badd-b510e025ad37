import { MOODLE_CONFIG, API_BASE_URL } from '../config/config';

// Configuration constants
const baseUrl = `${API_BASE_URL}${MOODLE_CONFIG.BASE_URL}`;
const defaultToken = MOODLE_CONFIG.DEFAULT_WS_TOKEN;
const defaultTenant = MOODLE_CONFIG.DEFAULT_TENANT;

export function buildApiUrl(wsfunction, params = {}, token = null) {
  const wstoken = token || defaultToken;
  const baseParams = {
    wstoken,
    wsfunction,
    moodlewsrestformat: 'json'
  };

  const allParams = { ...baseParams, ...params };
  const queryString = new URLSearchParams(allParams).toString();

  return `${baseUrl}?${queryString}`;
}

export async function callApi(wsfunction, params = {}, token = null) {
  try {
    const url = buildApiUrl(wsfunction, params, token);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP Error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.exception) {
      throw new Error(`Moodle API Error: ${data.message || data.exception}`);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

export async function getTenantHeaderInfo(tenantId = null, lang = 'en', token = null) {
  const params = {
    tenantid: tenantId || defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_TENANT_HEADER_INFO, params, token);
}

export async function getTenantFooterInfo(tenantId = null, lang = 'en', token = null) {
  const params = {
    tenantid: tenantId || defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_FOOTER_INFO, params, token);
}

export async function getTenantPrograms(tenantId = null, lang = 'en', token = null) {
  const params = {
    tenantid: tenantId || defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_PROGRAMS, params, token);
}

export async function getTenantClassrooms(tenantId = null, lang = 'en', token = null) {
  const params = {
    tenantid: tenantId || defaultTenant.id,
    lang
  };

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_CLASSROOMS, params, token);
}

export async function getTenantCourses(tenantId = null, lang = 'en', maxResults = null, token = null) {
  const params = {
    tenantid: tenantId || defaultTenant.id,
    lang
  };

  if (maxResults) {
    params.maxresults = maxResults;
  }

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_COURSES, params, token);
}

export async function getTenantInfo(url = null, code = null, lang = 'en', token = null) {
  const params = {
    url: url || window.location.hostname,
    lang
  };

  if (code) {
    params.code = code;
  }

  return await callApi(MOODLE_CONFIG.FUNCTIONS.GET_TENANT_INFO, params, token);
}

export async function testConnection(token = null) {
  try {
    await getTenantHeaderInfo(null, 'en', token);
    return true;
  } catch (error) {
    return false;
  }
}

// Default export object for backward compatibility
const multiTenantApi = {
  buildApiUrl,
  callApi,
  getTenantHeaderInfo,
  getTenantFooterInfo,
  getTenantPrograms,
  getTenantClassrooms,
  getTenantCourses,
  getTenantInfo,
  testConnection
};

export default multiTenantApi;
