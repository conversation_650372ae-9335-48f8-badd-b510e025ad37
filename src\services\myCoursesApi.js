import { API_BASE_URL, API_ENDPOINTS } from '../config/config';
import { getRequest } from '../core/api/fetcher';
import { handleEncryptedResponse } from 'utils/decryption';

// ==============================|| API - GET MY COURSES ||============================== //

export const getMyCourses = async () => {
  try {
    const response = await getRequest(API_ENDPOINTS.ENROLLED_COURSES);
    return handleEncryptedResponse(response);
  } catch (error) {
    console.error('Error fetching courses:', error);
    return {
      success: false,
      data: null,
      message: 'An error occurred while fetching courses'
    };
  }
};

// //post
// export const getMyCourses = async () => {
//   try {
//     const response = await getRequest(API_ENDPOINTS.ENROLLED_COURSES);
//     return handleEncryptedResponse(response);
//   } catch (error) {
//     console.error('Error fetching courses:', error);
//     return {
//       success: false,
//       data: null,
//       message: 'An error occurred while fetching courses'
//     };
//   }
// };
