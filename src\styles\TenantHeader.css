/* Multi-Tenant Header Styles */

.tenant-header {
  transition: all 0.3s ease;
}

.tenant-header.rtl {
  direction: rtl;
}

.tenant-header.ltr {
  direction: ltr;
}

/* Theme variations */
.tenant-header.light {
  background: linear-gradient(135deg, var(--primary-color, #8c76dd) 0%, var(--secondary-color, #FFE284) 100%);
}

.tenant-header.dark {
  background: linear-gradient(135deg, var(--primary-color, #333) 0%, var(--secondary-color, #555) 100%);
}

/* Responsive design */
@media (max-width: 768px) {
  .tenant-header .header-content {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }
  
  .tenant-header .tenant-info {
    order: 1;
  }
  
  .tenant-header .theme-colors {
    order: 2;
  }
  
  .tenant-header .actions {
    order: 3;
  }
}

/* Animation for color changes */
.color-circle {
  transition: all 0.3s ease;
}

.color-circle:hover {
  transform: scale(1.2);
  box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

/* Button hover effects */
.refresh-button {
  transition: all 0.2s ease;
}

.refresh-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Loading animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

/* Arabic font support */
.tenant-header[dir="rtl"] {
  font-family: 'Segoe UI', 'Tahoma', 'Arial', sans-serif;
}

.tenant-header[dir="rtl"] h1 {
  font-weight: bold;
}

/* Error state styles */
.error-header {
  background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
  border-left: 4px solid #f44336;
}

/* Success state styles */
.success-header {
  border-left: 4px solid #4caf50;
}

/* Tenant badge styles */
.tenant-badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Logo styles */
.tenant-logo {
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.tenant-logo:hover {
  transform: scale(1.05);
}
