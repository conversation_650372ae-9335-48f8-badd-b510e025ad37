/* Multi-Tenant Swiper Styles */
/* These styles will be dynamically updated based on tenant colors */

.mySwiper {
  width: 100%;
  padding-top: 50px;
  padding-bottom: 50px;
}

.mySwiper .swiper-slide {
  background-position: center;
  background-size: cover;
  width: 300px;
  height: 300px;
}

.mySwiper .swiper-slide img {
  display: block;
  width: 100%;
}

/* Pagination bullets with tenant colors */
.mySwiper .swiper-pagination-bullet {
  width: 12px;
  height: 12px;
  text-align: center;
  line-height: 12px;
  font-size: 12px;
  opacity: 1;
  background: var(--swiper-pagination-bullet-inactive-color, #cccccc);
  transition: all 0.3s ease;
}

.mySwiper .swiper-pagination-bullet-active {
  background: var(--swiper-pagination-color, #007aff) !important;
  transform: scale(1.2);
}

/* Hover effects */
.mySwiper .swiper-pagination-bullet:hover {
  transform: scale(1.1);
  opacity: 0.8;
}

/* Navigation arrows with tenant colors */
.mySwiper .swiper-button-next,
.mySwiper .swiper-button-prev {
  color: var(--swiper-navigation-color, var(--swiper-pagination-color, #007aff));
}

.mySwiper .swiper-button-next:after,
.mySwiper .swiper-button-prev:after {
  font-size: 20px;
  font-weight: 900;
}

/* Scrollbar with tenant colors */
.mySwiper .swiper-scrollbar-drag {
  background: var(--swiper-scrollbar-drag-bg-color, var(--swiper-pagination-color, #007aff));
}

/* Custom styles for different tenant themes */
.tenant-theme-primary {
  --swiper-pagination-color: var(--tenant-primary-color);
  --swiper-pagination-bullet-inactive-color: var(--tenant-secondary-color);
  --swiper-navigation-color: var(--tenant-primary-color);
}

.tenant-theme-secondary {
  --swiper-pagination-color: var(--tenant-secondary-color);
  --swiper-pagination-bullet-inactive-color: var(--tenant-tertiary-color);
  --swiper-navigation-color: var(--tenant-secondary-color);
}

/* Responsive design */
@media (max-width: 768px) {
  .mySwiper .swiper-pagination-bullet {
    width: 10px;
    height: 10px;
  }
  
  .mySwiper .swiper-button-next,
  .mySwiper .swiper-button-prev {
    display: none;
  }
}

@media (max-width: 480px) {
  .mySwiper {
    padding-top: 30px;
    padding-bottom: 30px;
  }
  
  .mySwiper .swiper-pagination-bullet {
    width: 8px;
    height: 8px;
    margin: 0 2px;
  }
}
