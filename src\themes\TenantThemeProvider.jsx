import React, { useEffect } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { useTenant } from '../contexts/TenantContext';
import { useMultiTenant } from '../hooks/useMultiTenant';
import { createTheme } from '@mui/material/styles';
import Default from './theme/default';

const TenantThemeProvider = ({ children }) => {
  const { currentTenant } = useTenant();

  const { tenantColors, isConnected, loading, headerInfo } = useMultiTenant();

  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎭 TenantThemeProvider - Colors from API:', {
        raw: headerInfo,
        processed: tenantColors,
        isConnected,
        loading
      });
    }
  }, [headerInfo, tenantColors, isConnected, loading]);

  const createTenantTheme = () => {
    // تحديد مصدر الألوان
    let colorSource = 'Default';
    let colorsToUse = {};

    if (isConnected && tenantColors.primary) {
      // استخدام ألوان من Multi-Tenant API
      colorsToUse = tenantColors;
      colorSource = 'Multi-Tenant API';
    } else if (currentTenant?.branding?.colors) {
      // استخدام ألوان من tenant config محلي
      colorsToUse = {
        primary: currentTenant.branding.colors.primary,
        secondary: currentTenant.branding.colors.secondary,
        tertiary: currentTenant.branding.colors.tertiary
      };
      colorSource = 'Local Tenant Config';
    }

    // Create palette using colors (will use defaults if colorsToUse is empty)
    const palette = Default('light', colorsToUse);

    // Create the complete theme
    const theme = createTheme({
      palette
    });

    // Log للتطوير
    if (process.env.NODE_ENV === 'development') {
      console.log('🎨 Theme Colors Applied:', {
        source: colorSource,
        colorsUsed: colorsToUse,
        finalPalette: {
          primary: palette.primary.main,
          secondary: palette.secondary.main,
          tertiary: palette.tertiary.main
        },
        apiStatus: {
          connected: isConnected,
          loading: loading,
          hasColors: !!tenantColors.primary
        }
      });
    }

    return theme;
  };

  const theme = createTenantTheme();

  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎭 TenantThemeProvider Status:', {
        multiTenantConnected: isConnected,
        multiTenantLoading: loading,
        colorsApplied: {
          primary: theme.palette.primary.main,
          secondary: theme.palette.secondary.main,
          tertiary: theme.palette.tertiary?.main
        }
      });
    }
  }, [theme, isConnected, loading]);

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

export default TenantThemeProvider;
