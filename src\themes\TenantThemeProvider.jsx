import React, { useEffect } from 'react';
import { ThemeProvider } from '@mui/material/styles';
import { useTenant } from '../contexts/TenantContext';
import { createTheme } from '@mui/material/styles';
import Default from './theme/default';

const TenantThemeProvider = ({ children }) => {
  const { currentTenant } = useTenant();

  const createTenantTheme = () => {
    // Determine color source
    let colorSource = 'Default';
    let colorsToUse = {};

    if (currentTenant?.primaryColor) {
      // Use colors from API tenant data
      colorsToUse = {
        primary: currentTenant.primaryColor,
        secondary: currentTenant.secondaryColor,
        tertiary: currentTenant.teritoryColor
      };
      colorSource = 'API Tenant Data';
    }

    // Create palette using colors (will use defaults if colorsToUse is empty)
    const palette = Default('light', colorsToUse);

    // Create the complete theme
    const theme = createTheme({
      palette
    });

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('🎨 Theme Colors Applied:', {
        source: colorSource,
        colorsUsed: colorsToUse,
        finalPalette: {
          primary: palette.primary.main,
          secondary: palette.secondary.main,
          tertiary: palette.tertiary.main
        },
        tenantData: {
          id: currentTenant?.id,
          name: currentTenant?.name,
          hasColors: !!currentTenant?.primaryColor
        }
      });
    }

    return theme;
  };

  const theme = createTenantTheme();

  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('🎭 TenantThemeProvider Status:', {
        tenantId: currentTenant?.id,
        tenantName: currentTenant?.name,
        colorsApplied: {
          primary: theme.palette.primary.main,
          secondary: theme.palette.secondary.main,
          tertiary: theme.palette.tertiary?.main
        }
      });
    }
  }, [theme, currentTenant]);

  return <ThemeProvider theme={theme}>{children}</ThemeProvider>;
};

export default TenantThemeProvider;
