import CryptoJS from 'crypto-js';

// ==============================|| DECRYPTION UTILITY ||============================== //

const DECRYPTION_KEY = import.meta.env.VITE_DECRYPTION_KEY;
/**
 * Decrypts an encrypted payload using AES-CBC
 * @param {string} encryptedData - The encrypted data in Base64 format
 * @param {string} iv - The initialization vector in Base64 format
 * @param {string} [key=DECRYPTION_KEY] - Optional custom decryption key
 * @returns {string} The decrypted data
 * @throws {Error} If decryption fails or parameters are invalid
 */
export const decryptPayload = (encryptedData, iv, key = DECRYPTION_KEY) => {
  if (!encryptedData || !iv) {
    throw new Error('Missing required decryption parameters');
  }

  try {
    const ivBytes = CryptoJS.enc.Base64.parse(iv);
    const encryptedBytes = CryptoJS.enc.Base64.parse(encryptedData);
    const keyBytes = CryptoJS.enc.Base64.parse(key);

    const decrypted = CryptoJS.AES.decrypt({ ciphertext: encryptedBytes }, keyBytes, {
      iv: ivBytes,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7
    });

    const result = decrypted.toString(CryptoJS.enc.Utf8);

    if (!result) {
      throw new Error('Decryption returned empty result');
    }

    return result;
  } catch (error) {
    console.error('Decryption error:', {
      encryptedData,
      iv,
      error: error.message
    });
    throw new Error('Failed to decrypt data: ' + error.message);
  }
};

/**
 * Handles an API response that may contain encrypted data
 * @param {Object} response - The API response object
 * @returns {Object} The processed response with decrypted data if applicable
 */
export const handleEncryptedResponse = (response) => {
  if (!response.success) {
    return response;
  }

  // Handle encrypted response
  if (response.data?.encrypted && response.data?.payload) {
    if (!response.data.payload.iv || !response.data.payload.data) {
      return {
        success: false,
        data: null,
        message: 'Invalid encrypted payload structure'
      };
    }

    try {
      const decryptedData = decryptPayload(response.data.payload.data, response.data.payload.iv);

      const parsedData = JSON.parse(decryptedData);
      return {
        success: true,
        data: parsedData
      };
    } catch (error) {
      console.error('Error decrypting response data:', error);
      return {
        success: false,
        data: null,
        message: 'Failed to decrypt response data'
      };
    }
  }

  // Return unencrypted response as is
  return response;
};
