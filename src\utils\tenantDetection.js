// src/utils/tenantDetection.js

/**
 * Detect tenant from subdomain
 * Example: company1.yourapp.com → company1
 */
export const detectTenantFromSubdomain = () => {
  const hostname = window.location.hostname;
  const parts = hostname.split('.');

  // If localhost or IP address
  if (hostname === 'localhost' || /^\d+\.\d+\.\d+\.\d+$/.test(hostname)) {
    return null;
  }

  // If more than two parts (e.g., company1.yourapp.com)
  if (parts.length >= 3) {
    const subdomain = parts[0].toLowerCase();
    // Reserved subdomains to ignore
    const reservedSubdomains = ['www', 'admin', 'api', 'mail', 'ftp'];

    if (!reservedSubdomains.includes(subdomain)) {
      return subdomain;
    }
  }

  return null;
};

/**
 * Detect tenant from URL path
 * Example: yourapp.com/company1/dashboard → company1
 */
export const detectTenantFromPath = () => {
  const pathSegments = window.location.pathname.split('/').filter((segment) => segment);

  // First segment in the path
  if (pathSegments.length > 0) {
    const firstSegment = pathSegments[0].toLowerCase();

    // Reserved paths to ignore
    const reservedPaths = ['login', 'register', 'forgot-password', 'admin', 'api', 'assets'];

    // Only return if it matches a known tenant
    if (!reservedPaths.includes(firstSegment) && DEMO_TENANTS[firstSegment]) {
      return firstSegment;
    }
  }

  return null;
};

/**
 * Detect tenant from query parameters
 * Example: yourapp.com?tenant=company1
 */
export const detectTenantFromQuery = () => {
  const urlParams = new URLSearchParams(window.location.search);
  const tenant = urlParams.get('tenant')?.toLowerCase();
  // Only return if it matches a known tenant
  return tenant && DEMO_TENANTS[tenant] ? tenant : null;
};

/**
 * Main function to detect tenant
 * Tries all methods in order of priority: query, subdomain, then path
 */
export const detectTenant = () => {
  console.log('Detecting tenant from URL:', window.location.href);

  // Priority 1: query parameter
  let tenant = detectTenantFromQuery();
  if (tenant) {
    console.log('Tenant detected from query:', tenant);
    return { type: 'query', value: tenant, url: window.location.search };
  }

  // Priority 2: subdomain
  tenant = detectTenantFromSubdomain();
  if (tenant) {
    console.log('Tenant detected from subdomain:', tenant);
    return { type: 'subdomain', value: tenant, url: window.location.hostname };
  }

  // Priority 3: path
  tenant = detectTenantFromPath();
  if (tenant) {
    console.log('Tenant detected from path:', tenant);
    return { type: 'path', value: tenant, url: window.location.pathname };
  }

  console.log('No tenant detected');
  return null;
};

/**
 * Create tenant URL
 */
export const createTenantUrl = (tenantId, type = 'subdomain') => {
  const currentUrl = window.location;

  switch (type) {
    case 'subdomain':
      // Create subdomain URL
      const hostname = currentUrl.hostname;
      const parts = hostname.split('.');
      if (parts.length >= 2) {
        const newHostname = `${tenantId}.${parts.slice(-2).join('.')}`;
        return `${currentUrl.protocol}//${newHostname}${currentUrl.pathname}`;
      }
      break;

    case 'path':
      // Create path-based URL
      return `${currentUrl.origin}/${tenantId}${currentUrl.pathname}`;

    case 'query':
      // Create query-based URL
      const url = new URL(currentUrl);
      url.searchParams.set('tenant', tenantId);
      return url.toString();

    default:
      return currentUrl.href;
  }

  return currentUrl.href;
};

/**
 * Demo tenants dictionary (for development/testing)
 * In production, this should come from an API or database
 */
export const DEMO_TENANTS = {
  company1: {
    id: 'company1',
    name: 'شركة الأول',
    domain: 'company1',
    branding: {
      logo: '/assets/images/company1-logo.png',
      colors: {
        primary: '#1976d2',
        secondary: '#dc004e'
      },
      companyName: 'شركة الأول للتكنولوجيا'
    },
    features: ['dashboard', 'users', 'reports']
  },
  company2: {
    id: 'company2',
    name: 'مؤسسة الثاني',
    domain: 'company2',
    branding: {
      logo: '/assets/images/company2-logo.png',
      colors: {
        primary: '#ff5722',
        secondary: '#4caf50'
      },
      companyName: 'مؤسسة الثاني للخدمات'
    },
    features: ['dashboard', 'users', 'analytics', 'reports']
  },
  company3: {
    id: 'company3',
    name: 'شركة الثالث',
    domain: 'company3',
    branding: {
      logo: '/assets/images/company3-logo.png',
      colors: {
        primary: '#9c27b0',
        secondary: '#ff9800'
      },
      companyName: 'شركة الثالث المحدودة'
    },
    features: ['dashboard', 'users']
  }
};

/**
 * Get tenant info from the demo dictionary
 * @param {string} tenantId
 * @returns {object|null}
 */
export const getTenantFromDemo = (tenantId) => {
  const normalizedId = tenantId?.toLowerCase();
  return DEMO_TENANTS[normalizedId] || null;
};
